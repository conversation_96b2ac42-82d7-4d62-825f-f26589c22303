#!/usr/bin/env python3
"""
Qdrant Vector Store for storing and searching code embeddings.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from dataclasses import asdict

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

from deepseek_embeddings import CodeEmbedding

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QdrantCodeStore:
    """
    Qdrant-based vector store for code embeddings with advanced search capabilities.
    """

    def __init__(self,
                 host: str = "localhost",
                 port: int = 6333,
                 collection_name: str = "code_embeddings",
                 vector_size: int = 384):
        """
        Initialize Qdrant code store

        Args:
            host: Qdrant server host
            port: Qdrant server port
            collection_name: Name of the collection
            vector_size: Dimension of embedding vectors
        """
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.vector_size = vector_size

        # Initialize client
        self.client = QdrantClient(host=host, port=port)

        # Create collection if it doesn't exist
        self._create_collection()

    def _create_collection(self):
        """Create Qdrant collection for code embeddings"""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if self.collection_name not in collection_names:
                # Create collection
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} already exists")

        except Exception as e:
            logger.error(f"Error creating collection: {e}")
            raise

    def add_embeddings(self, embeddings: List[CodeEmbedding]) -> bool:
        """
        Add code embeddings to the vector store

        Args:
            embeddings: List of CodeEmbedding objects

        Returns:
            True if successful, False otherwise
        """
        try:
            points = []

            for embedding in embeddings:
                # Create point
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding.embedding.tolist(),
                    payload={
                        "entity_id": embedding.entity_id,
                        "entity_name": embedding.entity_name,
                        "entity_type": embedding.entity_type,
                        "file_path": embedding.file_path,
                        "metadata": embedding.metadata
                    }
                )
                points.append(point)

            # Upsert points
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )

            logger.info(f"Added {len(points)} embeddings to Qdrant")
            return True

        except Exception as e:
            logger.error(f"Error adding embeddings: {e}")
            return False

    def search_similar(self,
                       query_embedding: np.ndarray,
                       limit: int = 10,
                       score_threshold: float = 0.0,
                       entity_type: Optional[str] = None,
                       file_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search for similar code embeddings

        Args:
            query_embedding: Query embedding vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            entity_type: Filter by entity type (class, function, method)
            file_path: Filter by file path

        Returns:
            List of search results with metadata
        """
        try:
            # Build filter conditions
            filter_conditions = []

            if entity_type:
                filter_conditions.append(
                    FieldCondition(
                        key="entity_type",
                        match=MatchValue(value=entity_type)
                    )
                )

            if file_path:
                filter_conditions.append(
                    FieldCondition(
                        key="file_path",
                        match=MatchValue(value=file_path)
                    )
                )

            # Create filter
            search_filter = None
            if filter_conditions:
                search_filter = Filter(must=filter_conditions)

            # Perform search
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True
            )

            # Format results
            formatted_results = []
            for result in results:
                formatted_result = {
                    "id": result.id,
                    "score": result.score,
                    "entity_id": result.payload["entity_id"],
                    "entity_name": result.payload["entity_name"],
                    "entity_type": result.payload["entity_type"],
                    "file_path": result.payload["file_path"],
                    "metadata": result.payload["metadata"]
                }
                formatted_results.append(formatted_result)

            logger.info(f"Found {len(formatted_results)} similar embeddings")
            return formatted_results

        except Exception as e:
            logger.error(f"Error searching embeddings: {e}")
            return []

    def search_by_text(self,
                       query_text: str,
                       embedder,
                       limit: int = 10,
                       entity_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search using text query (will be embedded first)

        Args:
            query_text: Text query to search for
            embedder: DeepSeekEmbedder instance
            limit: Maximum number of results
            entity_type: Filter by entity type

        Returns:
            List of search results
        """
        try:
            # Generate embedding for query
            query_embedding = embedder.embed_code(query_text, "query")

            # Search using embedding
            return self.search_similar(
                query_embedding=query_embedding,
                limit=limit,
                entity_type=entity_type
            )

        except Exception as e:
            logger.error(f"Error in text search: {e}")
            return []

    def get_entity_by_id(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific entity by its ID

        Args:
            entity_id: Entity identifier

        Returns:
            Entity data or None if not found
        """
        try:
            # Search by entity_id in payload
            results = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="entity_id",
                            match=MatchValue(value=entity_id)
                        )
                    ]
                ),
                limit=1,
                with_payload=True
            )

            if results[0]:  # results is a tuple (points, next_page_offset)
                point = results[0][0]
                return {
                    "id": point.id,
                    "entity_id": point.payload["entity_id"],
                    "entity_name": point.payload["entity_name"],
                    "entity_type": point.payload["entity_type"],
                    "file_path": point.payload["file_path"],
                    "metadata": point.payload["metadata"]
                }

            return None

        except Exception as e:
            logger.error(f"Error getting entity by ID: {e}")
            return None

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the collection"""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "total_points": info.points_count,
                "vector_size": info.config.params.vectors.size,
                "distance_metric": info.config.params.vectors.distance.value,
                "status": info.status.value
            }
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}

    def delete_by_file_path(self, file_path: str) -> bool:
        """
        Delete all embeddings for a specific file

        Args:
            file_path: Path of the file to delete embeddings for

        Returns:
            True if successful
        """
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="file_path",
                            match=MatchValue(value=file_path)
                        )
                    ]
                )
            )
            logger.info(f"Deleted embeddings for file: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error deleting embeddings for {file_path}: {e}")
            return False

    def clear_collection(self) -> bool:
        """Clear all embeddings from the collection"""
        try:
            self.client.delete_collection(self.collection_name)
            self._create_collection()
            logger.info(f"Cleared collection: {self.collection_name}")
            return True
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            return False


# Example usage
if __name__ == "__main__":
    # Initialize store
    store = QdrantCodeStore()

    # Get stats
    stats = store.get_collection_stats()
    print(f"Collection stats: {stats}")

    # Example search (would need actual embeddings)
    # results = store.search_by_text("fibonacci function", embedder)
    # print(f"Search results: {results}")
