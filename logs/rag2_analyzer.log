2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:22:59 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:22:59 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:22:59 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:22:59 | INFO     | rag2_analyzer.vector_db.vector_db_factory:create_vector_db:17 - Using Qdrant vector database
2025-07-18 00:22:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:__init__:312 - Initialized KnowledgeGraph with JSON file: ./data/knowledge_graph.json
2025-07-18 00:22:59 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:25 - Attempting to use Ollama LLM
2025-07-18 00:22:59 | INFO     | rag2_analyzer.llm.ollama_llm:_check_availability:31 - Ollama is available with model: llama3.2:3b
2025-07-18 00:22:59 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:28 - Using Ollama LLM
2025-07-18 00:22:59 | INFO     | rag2_analyzer.pipeline:initialize:40 - Initializing RAG 2.0 pipeline...
2025-07-18 00:22:59 | INFO     | rag2_analyzer.vector_db.qdrant_db:initialize:57 - Using existing Qdrant collection: code_chunks
2025-07-18 00:22:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:initialize:328 - Created new knowledge graph
2025-07-18 00:22:59 | INFO     | rag2_analyzer.pipeline:initialize:47 - Pipeline initialization complete
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 00:23:10 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 00:23:10 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 00:23:10 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 00:23:10 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 00:23:10 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected python for otp.py by extension
2025-07-18 00:23:10 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected python for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py: Medium confidence - 60.0% pattern match
2025-07-18 00:23:10 | WARNING  | rag2_analyzer.chunking.code_chunker:_chunk_python_file:130 - Syntax error in /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:23:10 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py: 1 chunks
2025-07-18 00:23:10 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 1
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 1 code chunks
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 00:23:10 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk af5d4b4d-c96b-4d62-ae0c-02fd306e12f3: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:23:10 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
import tkinter as tk
from tkinter import messagebox
import random
from datetime import datetime
import smtplib
from email.mime.text import MIMEText

user_name = "Sudheer  "
user_email = "sudheerss928@...
2025-07-18 00:23:10 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk af5d4b4d-c96b-4d62-ae0c-02fd306e12f3: ['Fixed incomplete try block - added except clause', "Could not fully correct syntax error: expected 'except' or 'finally' block (<unknown>, line 33)"]
2025-07-18 00:23:10 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk af5d4b4d-c96b-4d62-ae0c-02fd306e12f3
2025-07-18 00:23:10 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 3 Python entities using regex fallback
2025-07-18 00:23:10 | ERROR    | rag2_analyzer.extraction.ast_extractor:_extract_python_relationships:709 - Error extracting Python relationships from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 3 entities and 0 relationships
2025-07-18 00:23:10 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 00:23:16 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:20 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 1 chunks to Qdrant collection
2025-07-18 00:23:20 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 00:23:20 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:16:send_otp_email
2025-07-18 00:23:20 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:33:show_dashboard
2025-07-18 00:23:20 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:50:verify_otp
2025-07-18 00:23:20 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 3 entities to knowledge graph
2025-07-18 00:23:20 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 0 relationships to knowledge graph
2025-07-18 00:23:20 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 9.59 seconds
2025-07-18 00:23:31 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: find the error
2025-07-18 00:23:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:23:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:23:31 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'find the error', 'cleaned_query': 'find the error', 'keywords': ['find', 'error'], 'code_entities': [{'type': 'variable', 'name': 'find'}, {'type': 'variable', 'name': 'the'}, {'type': 'variable', 'name': 'error'}], 'intent': 'debug', 'language_hints': [], 'expanded_queries': ['find the error', 'find the exception', 'find the bug', 'find the issue', 'find the problem'], 'filters': {}}
2025-07-18 00:23:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:23:31 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:34 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:36 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:39 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:41 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:44 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:1 - Score: 0.559 - {'vector_similarity': 0.77289075, 'keyword_match': 0.25, 'entity_match': 0.3333333333333333, 'intent_relevance': 1.0, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:23:46 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:23:46 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.470, Valid=False, Issues=1
2025-07-18 00:23:46 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2720 chars)']
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.470
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:245 - Error correction requested - analyzing chunks for syntax errors
2025-07-18 00:23:46 | INFO     | rag2_analyzer.pipeline:query_codebase:264 - Found and corrected 1 syntax errors
2025-07-18 00:23:46 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:23:49 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 18.44 seconds
2025-07-18 00:24:15 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: find the error
line 33
    def show_dashboard():
SyntaxError: expected 'except' or 'finally' block

2025-07-18 00:24:15 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:24:15 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:24:15 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': "find the error\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n", 'cleaned_query': "find the error line 33 def show_dashboard(): SyntaxError: expected 'except' or 'finally' block", 'keywords': ['find', 'error', 'line', 'def', 'show_dashboard', 'syntaxerror', 'expected', 'except', 'finally', 'block'], 'code_entities': [{'type': 'class', 'name': 'SyntaxError'}, {'type': 'function', 'name': 'show_dashboard'}, {'type': 'variable', 'name': 'find'}, {'type': 'variable', 'name': 'the'}, {'type': 'variable', 'name': 'error'}, {'type': 'variable', 'name': 'line'}, {'type': 'variable', 'name': 'def'}, {'type': 'variable', 'name': 'show_dashboard'}, {'type': 'variable', 'name': 'expected'}, {'type': 'variable', 'name': 'except'}, {'type': 'variable', 'name': 'finally'}, {'type': 'variable', 'name': 'block'}], 'intent': 'debug', 'language_hints': [], 'expanded_queries': ["find the error\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n", "find the exception\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n", "find the bug\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n", "find the issue\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n", "find the problem\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block\n"], 'filters': {}}
2025-07-18 00:24:15 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:24:15 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:18 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:21 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:23 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:26 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:29 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:1 - Score: 0.609 - {'vector_similarity': 0.9092817, 'keyword_match': 0.15, 'entity_match': 0.5, 'intent_relevance': 1.0, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:24:31 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:24:31 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.455, Valid=False, Issues=1
2025-07-18 00:24:31 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2720 chars)']
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.455
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:245 - Error correction requested - analyzing chunks for syntax errors
2025-07-18 00:24:31 | INFO     | rag2_analyzer.pipeline:query_codebase:264 - Found and corrected 1 syntax errors
2025-07-18 00:24:31 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:24:33 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 18.39 seconds
2025-07-18 00:24:58 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: project overview

2025-07-18 00:24:58 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:24:58 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:24:58 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'project overview\n', 'cleaned_query': 'project overview', 'keywords': ['project', 'overview'], 'code_entities': [{'type': 'variable', 'name': 'project'}, {'type': 'variable', 'name': 'overview'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['project overview\n', 'main class', 'primary function', 'core functionality', 'key components', 'main method', 'application structure', 'project summary\n'], 'filters': {}}
2025-07-18 00:24:58 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:24:58 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:01 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:03 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:06 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:08 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:10 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:13 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:15 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:18 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpjiijz2tv/otp.py:1 - Score: 0.382 - {'vector_similarity': 0.76688874, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:25:20 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:25:20 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.390, Valid=False, Issues=1
2025-07-18 00:25:20 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2720 chars)']
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.390
2025-07-18 00:25:20 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:25:23 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 24.33 seconds
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:28:45 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:28:45 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:28:45 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:28:45 | INFO     | rag2_analyzer.vector_db.vector_db_factory:create_vector_db:17 - Using Qdrant vector database
2025-07-18 00:28:45 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:__init__:312 - Initialized KnowledgeGraph with JSON file: ./data/knowledge_graph.json
2025-07-18 00:28:45 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:25 - Attempting to use Ollama LLM
2025-07-18 00:28:45 | INFO     | rag2_analyzer.llm.ollama_llm:_check_availability:31 - Ollama is available with model: llama3.2:3b
2025-07-18 00:28:45 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:28 - Using Ollama LLM
2025-07-18 00:28:45 | INFO     | rag2_analyzer.pipeline:initialize:40 - Initializing RAG 2.0 pipeline...
2025-07-18 00:28:45 | INFO     | rag2_analyzer.vector_db.qdrant_db:initialize:57 - Using existing Qdrant collection: code_chunks
2025-07-18 00:28:45 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:load_from_json:453 - Loaded knowledge graph from ./data/knowledge_graph.json
2025-07-18 00:28:45 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:initialize:319 - Loaded existing knowledge graph with 0 classes and 0 entities
2025-07-18 00:28:45 | INFO     | rag2_analyzer.pipeline:initialize:47 - Pipeline initialization complete
2025-07-18 00:28:58 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 00:28:58 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 00:28:59 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 00:28:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 00:28:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected markdown for readme.md by extension
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected markdown for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/README.MD: Unknown confidence
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/README.MD: 1 chunks
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected python for main.py by extension
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected python for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py: Medium confidence - 60.0% pattern match
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large class 'Dinosaur' (2119 chars)
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_class_by_methods:749 - Split class 'Dinosaur' into 7 method-based chunks
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'main' (2004 chars)
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 4 smaller chunks
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'menu' (1073 chars)
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 2 smaller chunks
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py: 36 chunks
2025-07-18 00:28:59 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 37
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 37 code chunks
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 00:28:59 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 127c3235-da57-4c92-9897-862f500c9fc3: unexpected indent (<unknown>, line 1)
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        text = font.render("Points: " + str(points), True, (0, 0, 0))
        textRect = text.get_rect()
        textRect.center = (1000, 40)
        SCREEN.blit(text, textRect)

    def background():...
2025-07-18 00:28:59 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 127c3235-da57-4c92-9897-862f500c9fc3: ['Could not fully correct syntax error: unexpected indent (<unknown>, line 2)']
2025-07-18 00:28:59 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 127c3235-da57-4c92-9897-862f500c9fc3
2025-07-18 00:28:59 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 1 Python entities using regex fallback
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 72 entities and 95 relationships
2025-07-18 00:28:59 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 00:29:03 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:09 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 37 chunks to Qdrant collection
2025-07-18 00:29:09 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: cc46c681-2e15-4719-aa3f-ec2a46cbe015
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9fb17732-dadd-4807-acba-1f11786428b2
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a683128f-ea93-4289-bda2-45015ec48e00
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 449838b7-6430-4d1d-b5b6-be47b2bcef32
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 2a41d041-43ca-48ae-ab20-f1d23e3b056a
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: dd76c0b6-7f35-493f-9714-7d48411f3bee
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c82ff2f7-3196-49e6-b315-4cb9ce056391
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 7c2b7e00-58eb-48d3-b532-a5f8411bacfe
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b0d52ae7-4501-4be1-9e6c-50c2586e7850
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: ad9da513-3845-4956-8bed-61c664938c39
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0bada87c-96d1-43f5-93c9-29f979d3f1ad
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b66ce32d-ffec-4b44-9811-2d147e74b21c
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0212910b-581c-414d-af8c-63ef68768136
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1b5a39e3-c0a5-41cd-80fa-1dd8ad5a84e8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 068c2ec9-548a-4f1c-88aa-a8a9dc0bbb15
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 7286f330-f81a-4e7b-9866-3d86cfe61330
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 3dd1e2d3-83c5-424f-9416-092caf58b9b8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8a0fc046-4d1f-45cb-91fe-b3ae4bc341f4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1c66bdc5-e1a6-4ca6-9b3e-4246bb6d7cee
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 4e7eea70-277a-4606-8a2a-0540b40db53a
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8f14069d-54ef-48d9-a48d-31baa53901cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b8ae8bb7-5c7f-417a-a196-239aca9d8664
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9fd51402-9f14-42b3-a123-5807a9b7e1c8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1fe194b3-edd2-4e30-8189-fd71c2613e9d
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 11100824-1483-4ff0-851f-2dbe908dd3d6
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d6f0f887-0a5c-4b15-9f39-527b11478627
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0911e22e-dbb8-45fb-8723-cdf7ff36e965
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 38c6079e-ceee-4699-855b-d96057c20454
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9ab5973d-69fc-4ee5-96e5-33d8de7c78a9
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 4f3716da-9e25-49b5-90a8-7a4c910f073d
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0da376dd-7a3b-452a-b109-46306f228701
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8fee6f59-d509-45d0-9c8d-8100c38f8545
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d1bcecee-5151-474e-b414-61bd83298ce0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 7e56a3f6-234a-4d0b-8b51-836c291dc6d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 47e39cca-df1f-4316-a400-5c54d2fe0cf4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: e1e8413f-f025-49a6-8e2f-b19bf0f08c66
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:6:background
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9b7d3830-0c88-40bd-811d-111a0990e6cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 482eb146-6b52-452d-8637-65a2cf2965d8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 4979de97-5cb8-48f2-a0d8-c69162cf39ed
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 2e4bf93e-52af-42c3-854b-bbaf6bc16f45
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 542ecde0-e3e6-4ff0-b3a9-1beebb351048
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: cb601477-b290-4080-916f-7f57c26608a2
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: aa5c8299-570b-4795-8fbe-5f0b3efeb004
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fad24c7c-0463-47e4-9fda-38d7fe8c0825
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fcb07144-6b70-4258-abd9-106fecc0bccc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 37726b1d-e363-4cbc-a90a-e2b3a314846b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 46b162d5-0dc2-464f-9193-1e45ef834ee7
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: ae0dc994-9e7d-4f56-af08-4d851b8c8ca8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a9c55b71-257e-4dd7-aadd-d2564ee848fb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 32c9cc41-6955-4938-9902-64d07baf341f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d80cad27-2a91-434c-8902-cce2f06adada
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a498aea5-29f7-4741-97f3-9723904e2170
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0480395a-85bf-4c78-b3d2-682961592089
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 846bc269-5c46-45f3-8317-5999a60985cd
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: dbb552f1-650c-4901-b345-82c774854d37
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 6a429893-2f2a-4fd8-b396-8eeda7120304
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 03967f9a-82cf-49e8-a827-9a4d0c922efb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fb1d1577-f8da-41a4-9f39-0269233cae2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fc1c8ab3-c84b-4c99-b2ce-77e274cb84fc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0699c149-f579-4cfa-9865-4ae0cdef41d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fc81db77-14b1-47c9-837d-88548d963a55
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f79b0a81-e184-4db1-a74e-69f3aaee548c
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 72 entities to knowledge graph
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 4e7eea70-277a-4606-8a2a-0540b40db53a --inherits--> 7286f330-f81a-4e7b-9866-3d86cfe61330
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: b8ae8bb7-5c7f-417a-a196-239aca9d8664 --inherits--> 7286f330-f81a-4e7b-9866-3d86cfe61330
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 1fe194b3-edd2-4e30-8189-fd71c2613e9d --inherits--> 7286f330-f81a-4e7b-9866-3d86cfe61330
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> 6a429893-2f2a-4fd8-b396-8eeda7120304
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> ae0dc994-9e7d-4f56-af08-4d851b8c8ca8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> a9c55b71-257e-4dd7-aadd-d2564ee848fb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> 32c9cc41-6955-4938-9902-64d07baf341f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: cc46c681-2e15-4719-aa3f-ec2a46cbe015 --contains--> 0699c149-f579-4cfa-9865-4ae0cdef41d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: b66ce32d-ffec-4b44-9811-2d147e74b21c --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: b66ce32d-ffec-4b44-9811-2d147e74b21c --contains--> 6a429893-2f2a-4fd8-b396-8eeda7120304
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: b66ce32d-ffec-4b44-9811-2d147e74b21c --contains--> 0699c149-f579-4cfa-9865-4ae0cdef41d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 7286f330-f81a-4e7b-9866-3d86cfe61330 --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 7286f330-f81a-4e7b-9866-3d86cfe61330 --contains--> 6a429893-2f2a-4fd8-b396-8eeda7120304
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 7286f330-f81a-4e7b-9866-3d86cfe61330 --contains--> 0699c149-f579-4cfa-9865-4ae0cdef41d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 4e7eea70-277a-4606-8a2a-0540b40db53a --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: b8ae8bb7-5c7f-417a-a196-239aca9d8664 --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 1fe194b3-edd2-4e30-8189-fd71c2613e9d --contains--> a8f04725-e229-4431-b4c7-da643d8c469b
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 1fe194b3-edd2-4e30-8189-fd71c2613e9d --contains--> 0699c149-f579-4cfa-9865-4ae0cdef41d3
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --calls--> f79b0a81-e184-4db1-a74e-69f3aaee548c
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --calls--> fc81db77-14b1-47c9-837d-88548d963a55
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --calls--> 482eb146-6b52-452d-8637-65a2cf2965d8
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --calls--> 0911e22e-dbb8-45fb-8723-cdf7ff36e965
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> fcb07144-6b70-4258-abd9-106fecc0bccc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 8fee6f59-d509-45d0-9c8d-8100c38f8545
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 9b7d3830-0c88-40bd-811d-111a0990e6cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> aa5c8299-570b-4795-8fbe-5f0b3efeb004
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 4f3716da-9e25-49b5-90a8-7a4c910f073d
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 4f3716da-9e25-49b5-90a8-7a4c910f073d
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 0da376dd-7a3b-452a-b109-46306f228701
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 0da376dd-7a3b-452a-b109-46306f228701
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 9ab5973d-69fc-4ee5-96e5-33d8de7c78a9
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 47e39cca-df1f-4316-a400-5c54d2fe0cf4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 4f3716da-9e25-49b5-90a8-7a4c910f073d
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0911e22e-dbb8-45fb-8723-cdf7ff36e965 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> fcb07144-6b70-4258-abd9-106fecc0bccc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> 47e39cca-df1f-4316-a400-5c54d2fe0cf4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> 47e39cca-df1f-4316-a400-5c54d2fe0cf4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> fad24c7c-0463-47e4-9fda-38d7fe8c0825
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> aa5c8299-570b-4795-8fbe-5f0b3efeb004
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> fad24c7c-0463-47e4-9fda-38d7fe8c0825
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> aa5c8299-570b-4795-8fbe-5f0b3efeb004
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 482eb146-6b52-452d-8637-65a2cf2965d8 --uses--> 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 9b7d3830-0c88-40bd-811d-111a0990e6cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 9b7d3830-0c88-40bd-811d-111a0990e6cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 9b7d3830-0c88-40bd-811d-111a0990e6cb
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 8fee6f59-d509-45d0-9c8d-8100c38f8545
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 8fee6f59-d509-45d0-9c8d-8100c38f8545
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 6a429893-2f2a-4fd8-b396-8eeda7120304 --uses--> 99bd9e0a-6ee9-407a-92da-1579663cd1cc
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> c3d5bf72-8b9c-42b7-b4ed-c65d69578e2e
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> d336e460-fc10-4137-8f33-3cf8d0ab20b0
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> b2bd8f6f-7ae6-4250-a76c-93cca94dfb9f
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: fc81db77-14b1-47c9-837d-88548d963a55 --uses--> 1e301eb2-0059-4834-9e2a-811e8f53e8b5
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 8fee6f59-d509-45d0-9c8d-8100c38f8545
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 9eae5d2f-59ef-4b98-996f-f2465b522cb4
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 8faa33b2-63e1-49e2-a850-c54c58fe1d41
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: f79b0a81-e184-4db1-a74e-69f3aaee548c --uses--> 1eae211b-0bfd-449f-9003-62ae8a37e799
2025-07-18 00:29:09 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 95 relationships to knowledge graph
2025-07-18 00:29:09 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 10.41 seconds
2025-07-18 00:29:19 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: how many classes
2025-07-18 00:29:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:29:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:29:19 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'how many classes', 'cleaned_query': 'how many classes', 'keywords': ['how', 'many', 'classes'], 'code_entities': [{'type': 'variable', 'name': 'how'}, {'type': 'variable', 'name': 'many'}, {'type': 'variable', 'name': 'classes'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['how many classes'], 'filters': {'entity_types': ['class']}}
2025-07-18 00:29:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:29:19 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:22 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/README.MD:1 - Score: 0.540 - {'vector_similarity': 0.78763604, 'keyword_match': 0.3333333333333333, 'entity_match': 0.3333333333333333, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 2: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:32 - Score: 0.419 - {'vector_similarity': 0.8558792, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.6}
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 3: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:134 - Score: 0.414 - {'vector_similarity': 0.8335801, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:29:25 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 10 chunks, 0 entities, 0 relationships
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 10 chunks, 0 entities, 0 relationships
2025-07-18 00:29:25 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:206 - Counting query detected: 'how many classes' with 0 entities from retrieval
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:get_all_entities:788 - Retrieved 72 total entities from knowledge graph
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:209 - Knowledge graph contains 72 total entities
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:211 - Using 72 entities from knowledge graph instead of 0 from retrieval
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 10 chunks, 40 entities, 0 relationships
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.430, Valid=False, Issues=1
2025-07-18 00:29:25 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2785 chars)']
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.430
2025-07-18 00:29:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:29:27 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 7.24 seconds
2025-07-18 00:29:42 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: project overview

2025-07-18 00:29:42 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:29:42 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:29:42 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'project overview\n', 'cleaned_query': 'project overview', 'keywords': ['project', 'overview'], 'code_entities': [{'type': 'variable', 'name': 'project'}, {'type': 'variable', 'name': 'overview'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['project overview\n', 'main class', 'primary function', 'core functionality', 'key components', 'main method', 'application structure', 'project summary\n'], 'filters': {}}
2025-07-18 00:29:42 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:29:42 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:44 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:47 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:49 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:51 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:54 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:56 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:29:59 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:30:01 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:134 - Score: 0.422 - {'vector_similarity': 0.85133564, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 2: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:32 - Score: 0.415 - {'vector_similarity': 0.8454568, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.6}
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 3: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmps2ohnktb/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:138 - Score: 0.415 - {'vector_similarity': 0.83533573, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:30:04 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 00:30:04 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.565, Valid=True, Issues=0
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.565
2025-07-18 00:30:04 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:30:07 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 24.82 seconds
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:36:40 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 00:36:40 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 00:36:40 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 00:36:40 | INFO     | rag2_analyzer.vector_db.vector_db_factory:create_vector_db:17 - Using Qdrant vector database
2025-07-18 00:36:40 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:__init__:312 - Initialized KnowledgeGraph with JSON file: ./data/knowledge_graph.json
2025-07-18 00:36:40 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:25 - Attempting to use Ollama LLM
2025-07-18 00:36:40 | INFO     | rag2_analyzer.llm.ollama_llm:_check_availability:31 - Ollama is available with model: llama3.2:3b
2025-07-18 00:36:40 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:28 - Using Ollama LLM
2025-07-18 00:36:40 | INFO     | rag2_analyzer.pipeline:initialize:40 - Initializing RAG 2.0 pipeline...
2025-07-18 00:36:40 | INFO     | rag2_analyzer.vector_db.qdrant_db:initialize:57 - Using existing Qdrant collection: code_chunks
2025-07-18 00:36:40 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:load_from_json:453 - Loaded knowledge graph from ./data/knowledge_graph.json
2025-07-18 00:36:40 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:initialize:319 - Loaded existing knowledge graph with 0 classes and 0 entities
2025-07-18 00:36:40 | INFO     | rag2_analyzer.pipeline:initialize:47 - Pipeline initialization complete
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 00:36:47 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 00:36:47 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 00:36:47 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 00:36:47 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected java for currencyrateapi.java by extension
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected java for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java: Medium confidence - 64.3% pattern match
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_create_chunk_from_node:570 - Large class (2381 chars) - splitting into smaller chunks
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_split_large_content:629 - Created chunk part 1: 716 chars
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_split_large_content:629 - Created chunk part 2: 756 chars
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_split_large_content:629 - Created chunk part 3: 782 chars
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_split_large_content:629 - Created chunk part 4: 377 chars
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.chunking.code_chunker:_chunk_with_tree_sitter:155 - Tree-sitter extracted 9 chunks from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java: 9 chunks
2025-07-18 00:36:47 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 9
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 9 code chunks
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 6
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 4 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 2 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_java_entities_tree_sitter:1001 - Tree-sitter captures format: <class 'dict'>, length: 2
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_entities:168 - Tree-sitter extracted 1 entities from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | ERROR    | rag2_analyzer.extraction.ast_extractor:_extract_java_relationships_tree_sitter:1532 - Error extracting Java relationships with Tree-sitter: Impossible pattern at row 7, column 18
2025-07-18 00:36:47 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_tree_sitter_relationships:746 - Tree-sitter extracted 0 relationships from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 13 entities and 0 relationships
2025-07-18 00:36:47 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 00:36:52 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:36:56 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 9 chunks to Qdrant collection
2025-07-18 00:36:56 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:CurrencyRateAPI
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:2:logger
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:4:getApiKeyService
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:19:getURL
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:3:getURL
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:7:getJsonObject
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:10:getCurrencyCode
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:5:convert
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:getJsonObject
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:getApiKeyService
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:getURL
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:convert
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:0:getCurrencyCode
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 13 entities to knowledge graph
2025-07-18 00:36:56 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 0 relationships to knowledge graph
2025-07-18 00:36:56 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 8.56 seconds
2025-07-18 00:37:06 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: project overview
2025-07-18 00:37:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:37:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:37:06 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'project overview', 'cleaned_query': 'project overview', 'keywords': ['project', 'overview'], 'code_entities': [{'type': 'variable', 'name': 'project'}, {'type': 'variable', 'name': 'overview'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['project overview', 'main class', 'primary function', 'core functionality', 'key components', 'main method', 'application structure', 'project summary'], 'filters': {}}
2025-07-18 00:37:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:37:06 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:09 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:11 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:14 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:16 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:19 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:21 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:24 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:27 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:38 - Score: 0.420 - {'vector_similarity': 0.8483625, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 2: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:72 - Score: 0.397 - {'vector_similarity': 0.790846, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 3: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpcqrr03cb/CurrencyRateAPI.java:67 - Score: 0.396 - {'vector_similarity': 0.7982365, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.6}
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:37:29 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 7 chunks, 0 entities, 0 relationships
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 7 chunks, 0 entities, 0 relationships
2025-07-18 00:37:29 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 7 chunks, 0 entities, 0 relationships
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.379, Valid=False, Issues=0
2025-07-18 00:37:29 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: []
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.379
2025-07-18 00:37:29 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:37:34 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 28.22 seconds
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 00:38:08 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 00:38:08 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 00:38:08 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 00:38:08 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected python for otp.py by extension
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected python for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py: Medium confidence - 60.0% pattern match
2025-07-18 00:38:08 | WARNING  | rag2_analyzer.chunking.code_chunker:_chunk_python_file:130 - Syntax error in /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py: 1 chunks
2025-07-18 00:38:08 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 1
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 1 code chunks
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 00:38:08 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 5740124c-dd3e-491a-a4d0-b1c91f71e922: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
import tkinter as tk
from tkinter import messagebox
import random
from datetime import datetime
import smtplib
from email.mime.text import MIMEText

user_name = "Sudheer  "
user_email = "sudheerss928@...
2025-07-18 00:38:08 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 5740124c-dd3e-491a-a4d0-b1c91f71e922: ['Fixed incomplete try block - added except clause', "Could not fully correct syntax error: expected 'except' or 'finally' block (<unknown>, line 33)"]
2025-07-18 00:38:08 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 5740124c-dd3e-491a-a4d0-b1c91f71e922
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 3 Python entities using regex fallback
2025-07-18 00:38:08 | ERROR    | rag2_analyzer.extraction.ast_extractor:_extract_python_relationships:709 - Error extracting Python relationships from /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py: expected 'except' or 'finally' block (<unknown>, line 33)
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 3 entities and 0 relationships
2025-07-18 00:38:08 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 00:38:08 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:13 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 1 chunks to Qdrant collection
2025-07-18 00:38:13 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 00:38:13 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py:16:send_otp_email
2025-07-18 00:38:13 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py:33:show_dashboard
2025-07-18 00:38:13 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py:50:verify_otp
2025-07-18 00:38:13 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 3 entities to knowledge graph
2025-07-18 00:38:13 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 0 relationships to knowledge graph
2025-07-18 00:38:13 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 4.31 seconds
2025-07-18 00:38:25 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: find the error
2025-07-18 00:38:25 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:38:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:38:25 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'find the error', 'cleaned_query': 'find the error', 'keywords': ['find', 'error'], 'code_entities': [{'type': 'variable', 'name': 'find'}, {'type': 'variable', 'name': 'the'}, {'type': 'variable', 'name': 'error'}], 'intent': 'debug', 'language_hints': [], 'expanded_queries': ['find the error', 'find the exception', 'find the bug', 'find the issue', 'find the problem'], 'filters': {}}
2025-07-18 00:38:25 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:38:25 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:28 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:30 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:33 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:35 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:38 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py:1 - Score: 0.559 - {'vector_similarity': 0.77289075, 'keyword_match': 0.25, 'entity_match': 0.3333333333333333, 'intent_relevance': 1.0, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:38:40 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:38:40 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.470, Valid=False, Issues=1
2025-07-18 00:38:40 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2720 chars)']
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.470
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:245 - Error correction requested - analyzing chunks for syntax errors
2025-07-18 00:38:40 | INFO     | rag2_analyzer.pipeline:query_codebase:264 - Found and corrected 1 syntax errors
2025-07-18 00:38:40 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:38:42 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 16.91 seconds
2025-07-18 00:38:49 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: find the error
line 33
    def show_dashboard():
SyntaxError: expected 'except' or 'finally' block
2025-07-18 00:38:49 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 00:38:49 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 00:38:49 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': "find the error\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block", 'cleaned_query': "find the error line 33 def show_dashboard(): SyntaxError: expected 'except' or 'finally' block", 'keywords': ['find', 'error', 'line', 'def', 'show_dashboard', 'syntaxerror', 'expected', 'except', 'finally', 'block'], 'code_entities': [{'type': 'class', 'name': 'SyntaxError'}, {'type': 'function', 'name': 'show_dashboard'}, {'type': 'variable', 'name': 'find'}, {'type': 'variable', 'name': 'the'}, {'type': 'variable', 'name': 'error'}, {'type': 'variable', 'name': 'line'}, {'type': 'variable', 'name': 'def'}, {'type': 'variable', 'name': 'show_dashboard'}, {'type': 'variable', 'name': 'expected'}, {'type': 'variable', 'name': 'except'}, {'type': 'variable', 'name': 'finally'}, {'type': 'variable', 'name': 'block'}], 'intent': 'debug', 'language_hints': [], 'expanded_queries': ["find the error\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block", "find the exception\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block", "find the bug\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block", "find the issue\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block", "find the problem\nline 33\n    def show_dashboard():\nSyntaxError: expected 'except' or 'finally' block"], 'filters': {}}
2025-07-18 00:38:49 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 00:38:49 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:53 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:56 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:38:58 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:39:01 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:39:03 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp6e50d8r1/otp.py:1 - Score: 0.609 - {'vector_similarity': 0.9092817, 'keyword_match': 0.15, 'entity_match': 0.5, 'intent_relevance': 1.0, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 00:39:06 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:39:06 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 1 chunks, 0 entities, 0 relationships
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.455, Valid=False, Issues=1
2025-07-18 00:39:06 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 1 is too long (2720 chars)']
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.455
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:245 - Error correction requested - analyzing chunks for syntax errors
2025-07-18 00:39:06 | INFO     | rag2_analyzer.pipeline:query_codebase:264 - Found and corrected 1 syntax errors
2025-07-18 00:39:06 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 00:39:08 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 18.48 seconds
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 11:06:58 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: java
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: javascript
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: typescript
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: cpp
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: c
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: csharp
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: go
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: rust
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: php
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: ruby
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: swift
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: kotlin
2025-07-18 11:06:58 | DEBUG    | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:144 - Loaded Tree-sitter language: scala
2025-07-18 11:06:58 | INFO     | rag2_analyzer.parsing.tree_sitter_parser:_initialize_languages:150 - Tree-sitter initialized with 13 languages
2025-07-18 11:06:58 | INFO     | rag2_analyzer.vector_db.vector_db_factory:create_vector_db:17 - Using Qdrant vector database
2025-07-18 11:06:58 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:__init__:312 - Initialized KnowledgeGraph with JSON file: ./data/knowledge_graph.json
2025-07-18 11:06:58 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:25 - Attempting to use Ollama LLM
2025-07-18 11:06:58 | INFO     | rag2_analyzer.llm.ollama_llm:_check_availability:31 - Ollama is available with model: llama3.2:3b
2025-07-18 11:06:58 | INFO     | rag2_analyzer.llm.llm_factory:create_llm:28 - Using Ollama LLM
2025-07-18 11:06:58 | INFO     | rag2_analyzer.pipeline:initialize:40 - Initializing RAG 2.0 pipeline...
2025-07-18 11:06:59 | INFO     | rag2_analyzer.vector_db.qdrant_db:initialize:57 - Using existing Qdrant collection: code_chunks
2025-07-18 11:06:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:load_from_json:453 - Loaded knowledge graph from ./data/knowledge_graph.json
2025-07-18 11:06:59 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:initialize:319 - Loaded existing knowledge graph with 0 classes and 0 entities
2025-07-18 11:06:59 | INFO     | rag2_analyzer.pipeline:initialize:47 - Pipeline initialization complete
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 11:07:05 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 11:07:05 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 11:07:05 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 11:07:05 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected markdown for readme.md by extension
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected markdown for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/README.MD: Unknown confidence
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/README.MD: 1 chunks
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected python for main.py by extension
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected python for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py: Medium confidence - 60.0% pattern match
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large class 'Dinosaur' (2119 chars)
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_class_by_methods:749 - Split class 'Dinosaur' into 7 method-based chunks
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'main' (2004 chars)
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 4 smaller chunks
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'menu' (1073 chars)
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 2 smaller chunks
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py: 36 chunks
2025-07-18 11:07:05 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 37
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 37 code chunks
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 11:07:05 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 369e312a-fc23-4ffd-81ac-8ffb0f6c2c11: unexpected indent (<unknown>, line 1)
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        text = font.render("Points: " + str(points), True, (0, 0, 0))
        textRect = text.get_rect()
        textRect.center = (1000, 40)
        SCREEN.blit(text, textRect)

    def background():...
2025-07-18 11:07:05 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 369e312a-fc23-4ffd-81ac-8ffb0f6c2c11: ['Could not fully correct syntax error: unexpected indent (<unknown>, line 2)']
2025-07-18 11:07:05 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 369e312a-fc23-4ffd-81ac-8ffb0f6c2c11
2025-07-18 11:07:05 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 1 Python entities using regex fallback
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 72 entities and 95 relationships
2025-07-18 11:07:05 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 11:07:11 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:17 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 37 chunks to Qdrant collection
2025-07-18 11:07:17 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a7ceb1d8-263f-4b97-8993-ffe08a40cd17
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d5beefc6-46b7-438c-a7a3-d8a79b6135b4
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: e1b20bb6-af68-4406-8f1b-a2c79323d485
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 2d21589c-7b26-49c2-90a3-64092a74310a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c45b732e-7b04-49c9-bcca-9e9d5ee22dc6
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: aa7874f9-796a-42ae-9b62-20ead649bc2c
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 48328d3f-49e6-409d-b54b-025bfd688843
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 000a2050-b24c-442c-9c9f-34995c654cbf
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c10e02f0-018d-47a6-8dad-4577033b9178
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 18f375d6-8fee-44a9-aa6d-2950586af4e9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 22776ba4-530c-45a5-874b-d22d06b68c70
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: e2e10cf2-63cf-4d06-8d5a-27e965628211
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 2038d58c-be31-4673-ac7b-389d6a11d807
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f092e2da-6d58-4490-8b29-b04427a7ce02
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c4ccd0b4-cc37-4388-83f1-1b7e9e17bab0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0235a6e7-d273-4c3b-80d9-50abde7910ac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 2cb7b898-c071-4ebb-95d4-f3c914438ea5
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: afb9281b-48d0-46d0-8e8c-5df0cf02ae94
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c0a6d908-b898-4162-bd9a-3a37dbf8cb10
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: ca4dca6a-9c9d-49e8-a4d9-e383d6866123
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 063d80d7-f1ec-4052-b503-27b80d038713
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 1d0a93a8-27cc-43ec-be0e-d93d1756ca0c
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: afdc70a6-cf88-4c0b-b0d9-93048bb208df
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 521eedc2-2bb9-4894-976d-ea893c505a26
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 5b28698b-0cc1-4666-8f51-9ddee7004f4f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 858a44e3-168d-4b90-a3d9-edb34f5427bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 394e2625-ea8e-46bd-a9af-0adbefb8451d
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8df63a79-db77-45e5-a5c5-fc147cdead3d
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 4ff0421f-2fa1-47d6-848a-0b1d715b9bea
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 10972941-8538-42e6-997a-fb60108e94f1
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b9d7fc67-acdb-4bce-b9e0-8ac7ce87c8ff
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 3a06f659-79bf-4905-8550-2a76e1ded0dd
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 8b927f4d-9446-40e7-9bc7-577ee2b023be
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c900f9b4-ea2d-41a6-bbc5-88530f8c0001
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d852a46b-6957-42aa-897c-f3daed7111d7
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: a84c73b7-0802-45e4-b727-cf1c5b50fa64
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:6:background
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b5857239-1325-4621-bbbb-dac62fd30d82
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 38d12cb6-f79e-417a-91d9-60909f1ebf40
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 28fd93a9-1791-4ed0-b969-b4b7a9a0144a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9da6d78b-b6c5-4dc6-96d1-e52535bc40b1
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f5d2fa61-5864-4fde-8c20-e788b9d459f0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: fee2fc86-88ff-47fb-8ffa-45915c192c29
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b376cc20-f1d9-42b8-b732-58e45f3c8b44
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 15c1f195-1ce4-4e6d-9392-605830667ea2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 575c9607-877a-412e-842b-98dff1354f2a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 85d0c9ef-e958-4a5a-b8dd-a3f225309062
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 5a5aeb11-4d21-4da9-9947-ce9d66976268
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f7b63885-44b8-4d49-a44e-8b5b2abf4930
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d88b2b06-4835-4f4c-b51a-6f3667537b65
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 6b395bc1-7265-4cd5-b2e2-af2493be5ca3
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 5121e1cf-dc10-4403-b0e3-f6f01c7a4694
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 964bfe16-7e40-4902-ae87-ebffa4900107
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 6ed81585-40ff-43d4-a542-9e408f1cd727
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: ee19cf32-f5b9-4ea1-bbc8-947c957796bf
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 03d23267-4af0-46f1-8948-76497cc729ef
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c70277de-4be5-4711-a08a-18f33a595af2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 27cf1fe4-30ee-450f-8b46-94bbc2f3dc11
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: b9f26cf0-0254-4545-b97a-c6bcd463999a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0b04a450-f2be-4d0f-b59b-947b759b85ef
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 91a426e1-9010-4bc3-8387-9a6c2ced0fa2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 9979ff62-2c24-477b-b424-afd317de7fae
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: ca97a5fd-11f1-493e-8db8-a59255228e1c
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 72 entities to knowledge graph
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca4dca6a-9c9d-49e8-a4d9-e383d6866123 --inherits--> 0235a6e7-d273-4c3b-80d9-50abde7910ac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 1d0a93a8-27cc-43ec-be0e-d93d1756ca0c --inherits--> 0235a6e7-d273-4c3b-80d9-50abde7910ac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 521eedc2-2bb9-4894-976d-ea893c505a26 --inherits--> 0235a6e7-d273-4c3b-80d9-50abde7910ac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> c70277de-4be5-4711-a08a-18f33a595af2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> f7b63885-44b8-4d49-a44e-8b5b2abf4930
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> d88b2b06-4835-4f4c-b51a-6f3667537b65
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> 6b395bc1-7265-4cd5-b2e2-af2493be5ca3
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: a7ceb1d8-263f-4b97-8993-ffe08a40cd17 --contains--> 91a426e1-9010-4bc3-8387-9a6c2ced0fa2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: e2e10cf2-63cf-4d06-8d5a-27e965628211 --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: e2e10cf2-63cf-4d06-8d5a-27e965628211 --contains--> c70277de-4be5-4711-a08a-18f33a595af2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: e2e10cf2-63cf-4d06-8d5a-27e965628211 --contains--> 91a426e1-9010-4bc3-8387-9a6c2ced0fa2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0235a6e7-d273-4c3b-80d9-50abde7910ac --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0235a6e7-d273-4c3b-80d9-50abde7910ac --contains--> c70277de-4be5-4711-a08a-18f33a595af2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 0235a6e7-d273-4c3b-80d9-50abde7910ac --contains--> 91a426e1-9010-4bc3-8387-9a6c2ced0fa2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca4dca6a-9c9d-49e8-a4d9-e383d6866123 --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 1d0a93a8-27cc-43ec-be0e-d93d1756ca0c --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 521eedc2-2bb9-4894-976d-ea893c505a26 --contains--> 987904d6-7a13-4a3b-8a65-a7a8637b8aac
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 521eedc2-2bb9-4894-976d-ea893c505a26 --contains--> 91a426e1-9010-4bc3-8387-9a6c2ced0fa2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --calls--> ca97a5fd-11f1-493e-8db8-a59255228e1c
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --calls--> 9979ff62-2c24-477b-b424-afd317de7fae
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --calls--> 38d12cb6-f79e-417a-91d9-60909f1ebf40
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --calls--> 394e2625-ea8e-46bd-a9af-0adbefb8451d
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 575c9607-877a-412e-842b-98dff1354f2a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 3a06f659-79bf-4905-8550-2a76e1ded0dd
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b5857239-1325-4621-bbbb-dac62fd30d82
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b376cc20-f1d9-42b8-b732-58e45f3c8b44
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 10972941-8538-42e6-997a-fb60108e94f1
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 10972941-8538-42e6-997a-fb60108e94f1
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b9d7fc67-acdb-4bce-b9e0-8ac7ce87c8ff
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b9d7fc67-acdb-4bce-b9e0-8ac7ce87c8ff
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 4ff0421f-2fa1-47d6-848a-0b1d715b9bea
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> d852a46b-6957-42aa-897c-f3daed7111d7
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 10972941-8538-42e6-997a-fb60108e94f1
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 394e2625-ea8e-46bd-a9af-0adbefb8451d --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 575c9607-877a-412e-842b-98dff1354f2a
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> d852a46b-6957-42aa-897c-f3daed7111d7
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> d852a46b-6957-42aa-897c-f3daed7111d7
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 15c1f195-1ce4-4e6d-9392-605830667ea2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> b376cc20-f1d9-42b8-b732-58e45f3c8b44
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> 15c1f195-1ce4-4e6d-9392-605830667ea2
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> b376cc20-f1d9-42b8-b732-58e45f3c8b44
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 38d12cb6-f79e-417a-91d9-60909f1ebf40 --uses--> b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> b5857239-1325-4621-bbbb-dac62fd30d82
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> b5857239-1325-4621-bbbb-dac62fd30d82
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> b5857239-1325-4621-bbbb-dac62fd30d82
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> 3a06f659-79bf-4905-8550-2a76e1ded0dd
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> 3a06f659-79bf-4905-8550-2a76e1ded0dd
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: c70277de-4be5-4711-a08a-18f33a595af2 --uses--> 9ae8558f-f95f-4e93-ad77-5580d20eba39
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> 07aa998d-036b-4532-b32f-8d8560a08f4b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> c72bf278-6dea-4ea9-b01a-397b6d8457a9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> 115f7840-1cc7-4ef8-9a07-5449320c90bc
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: 9979ff62-2c24-477b-b424-afd317de7fae --uses--> b76cb3a7-1443-420b-98c3-2ff1c0a7743f
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 3a06f659-79bf-4905-8550-2a76e1ded0dd
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> f1a0b472-490a-4520-9864-d20a6367ce5b
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 62911c0e-6b04-496c-8c61-a76693cacad9
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relation:402 - Added relationship: ca97a5fd-11f1-493e-8db8-a59255228e1c --uses--> 0a3ba41b-4058-4e8c-87c6-5ae9127153c0
2025-07-18 11:07:17 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 95 relationships to knowledge graph
2025-07-18 11:07:17 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 11.71 seconds
2025-07-18 11:07:30 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: what is project overview
2025-07-18 11:07:30 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 11:07:30 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 11:07:30 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'what is project overview', 'cleaned_query': 'what is project overview', 'keywords': ['what', 'project', 'overview'], 'code_entities': [{'type': 'variable', 'name': 'what'}, {'type': 'variable', 'name': 'project'}, {'type': 'variable', 'name': 'overview'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['what is project overview', 'main class', 'primary function', 'core functionality', 'key components', 'main method', 'application structure', 'what is project summary'], 'filters': {}}
2025-07-18 11:07:30 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 11:07:30 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:32 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:34 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:37 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:39 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:42 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:44 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:46 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:49 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:134 - Score: 0.438 - {'vector_similarity': 0.89271957, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 2: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:32 - Score: 0.435 - {'vector_similarity': 0.8951616, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.6}
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 3: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmp5wvjel3h/extracted_chrome-dinosaur-master/chrome-dinosaur-master/main.py:159 - Score: 0.434 - {'vector_similarity': 0.8822059, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 11:07:51 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 11:07:51 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 9 chunks, 0 entities, 0 relationships
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.523, Valid=True, Issues=0
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.523
2025-07-18 11:07:51 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 11:07:55 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 25.27 seconds
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:clear_previous_data:120 - Clearing previous analysis data...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:295 - Deleted collection: code_chunks
2025-07-18 11:10:43 | INFO     | rag2_analyzer.vector_db.qdrant_db:clear_all:308 - Recreated empty collection: code_chunks
2025-07-18 11:10:43 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:save_to_json:423 - Saved knowledge graph to ./data/knowledge_graph.json
2025-07-18 11:10:43 | INFO     | rag2_analyzer.knowledge_graph.networkx_graph:clear_all:918 - Cleared all knowledge graph data
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:clear_previous_data:133 - Previous data cleared successfully
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:68 - Processing codebase: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:71 - Step 1: Chunking code...
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.utils.enhanced_language_detector:detect_language:191 - Detected python for push the box.py by extension
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_file:45 - Detected python for /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1/push the box.py: Medium confidence - 80.0% pattern match
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large class 'Game' (12376 chars)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large method into 1 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large method into 3 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large method into 5 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_class_by_methods:749 - Split class 'Game' into 10 method-based chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function '__init__' (1274 chars)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 1 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'process_level' (3269 chars)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 3 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_create_chunk_from_ast_node:601 - Splitting large function 'actions' (7133 chars)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:_split_large_chunk:843 - Split large function into 5 smaller chunks
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.chunking.code_chunker:chunk_directory:99 - Chunked /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1/push the box.py: 20 chunks
2025-07-18 11:10:43 | INFO     | rag2_analyzer.chunking.code_chunker:chunk_directory:101 - Total chunks generated: 20
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:83 - Generated 20 code chunks
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:86 - Step 2: Extracting entities and relationships...
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 496870d9-a6a8-47e0-bd83-a78625f6538f: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
                elif self.x_level == "P": # if the character is "P" -> it is the player
                    self.x_player = int(WIDTH/2) + int(((self.x_counter - 7) * TILE_SIZE)) # does exactly the sa...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 496870d9-a6a8-47e0-bd83-a78625f6538f: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 496870d9-a6a8-47e0-bd83-a78625f6538f
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk fcfc38a9-bc93-4108-aa9f-2f10bbe2a769: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
                self.x_counter += 1
            
            self.x_counter = 0
            self.y_counter += 1

        self.level_layout.close() # the text file closes so it would not create any fur...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk fcfc38a9-bc93-4108-aa9f-2f10bbe2a769: ['Could not fully correct syntax error: unexpected indent (<unknown>, line 3)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk fcfc38a9-bc93-4108-aa9f-2f10bbe2a769
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 890c63f8-80bb-41c7-8691-113e23b2aea7: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "a": # if te pressed key is A
            # the program, will do exactly the same process as before, but instead of calculating movement upwards, it will do it for the left ...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 890c63f8-80bb-41c7-8691-113e23b2aea7: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 890c63f8-80bb-41c7-8691-113e23b2aea7
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk d5ca3da2-e843-4298-a73e-ebd4997b369b: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "s": # if the pressed key is S
            # calculating downward movements
            if ((canvas.coords(self.player)[0], canvas.coords(self.player)[1] + TILE_SIZE) in sel...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk d5ca3da2-e843-4298-a73e-ebd4997b369b: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk d5ca3da2-e843-4298-a73e-ebd4997b369b
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk a2a58a13-f5f7-4ed3-bc77-1e203692a4f7: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "d": # if the pressed key is D
            # movements for the right side
            if ((canvas.coords(self.player)[0] + TILE_SIZE, canvas.coords(self.player)[1]) in self....
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk a2a58a13-f5f7-4ed3-bc77-1e203692a4f7: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk a2a58a13-f5f7-4ed3-bc77-1e203692a4f7
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk e5b886a3-6341-4a56-8af4-ac93f534ce45: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "r": # if the player pressed R
            canvas.move(self.player, -self.x_player_change, -self.y_player_change) # the player will be moved in reversed directions of the ca...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk e5b886a3-6341-4a56-8af4-ac93f534ce45: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk e5b886a3-6341-4a56-8af4-ac93f534ce45
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk ac24e9af-2562-412e-9ca2-66188fe42f5e: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
                elif self.x_level == "P": # if the character is "P" -> it is the player
                    self.x_player = int(WIDTH/2) + int(((self.x_counter - 7) * TILE_SIZE)) # does exactly the sa...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk ac24e9af-2562-412e-9ca2-66188fe42f5e: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk ac24e9af-2562-412e-9ca2-66188fe42f5e
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk dd4c3044-1ba4-4193-b331-b3b48ef5db7b: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
                self.x_counter += 1
            
            self.x_counter = 0
            self.y_counter += 1

        self.level_layout.close() # the text file closes so it would not create any fur...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk dd4c3044-1ba4-4193-b331-b3b48ef5db7b: ['Could not fully correct syntax error: unexpected indent (<unknown>, line 3)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk dd4c3044-1ba4-4193-b331-b3b48ef5db7b
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 2be628dc-682a-451c-b39b-103225f4be78: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "a": # if te pressed key is A
            # the program, will do exactly the same process as before, but instead of calculating movement upwards, it will do it for the left ...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 2be628dc-682a-451c-b39b-103225f4be78: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 2be628dc-682a-451c-b39b-103225f4be78
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 1ff2c0e0-9c03-4675-8a94-93abb500978d: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "s": # if the pressed key is S
            # calculating downward movements
            if ((canvas.coords(self.player)[0], canvas.coords(self.player)[1] + TILE_SIZE) in sel...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 1ff2c0e0-9c03-4675-8a94-93abb500978d: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 1ff2c0e0-9c03-4675-8a94-93abb500978d
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 9f01a484-5432-42ea-85b6-aef740d92b63: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "d": # if the pressed key is D
            # movements for the right side
            if ((canvas.coords(self.player)[0] + TILE_SIZE, canvas.coords(self.player)[1]) in self....
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 9f01a484-5432-42ea-85b6-aef740d92b63: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 9f01a484-5432-42ea-85b6-aef740d92b63
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:103 - Syntax error in chunk 710d030b-b553-405a-a530-96b1957aec9e: unexpected indent (<unknown>, line 1)
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:104 - Problematic content:
        elif event.char == "r": # if the player pressed R
            canvas.move(self.player, -self.x_player_change, -self.y_player_change) # the player will be moved in reversed directions of the ca...
2025-07-18 11:10:43 | INFO     | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:111 - Corrected syntax errors in chunk 710d030b-b553-405a-a530-96b1957aec9e: ['Fixed syntax errors', 'Could not fully correct syntax error: invalid syntax (<unknown>, line 1)']
2025-07-18 11:10:43 | WARNING  | rag2_analyzer.extraction.ast_extractor:_extract_python_entities:129 - Corrected code still has syntax errors for chunk 710d030b-b553-405a-a530-96b1957aec9e
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.extraction.ast_extractor:_extract_python_entities_regex:251 - Extracted 0 Python entities using regex fallback
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:92 - Extracted 8 entities and 0 relationships
2025-07-18 11:10:43 | INFO     | rag2_analyzer.pipeline:process_codebase:95 - Step 3: Storing chunks in vector database...
2025-07-18 11:10:43 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:10:49 | INFO     | rag2_analyzer.vector_db.qdrant_db:add_chunks:113 - Added 20 chunks to Qdrant collection
2025-07-18 11:10:49 | INFO     | rag2_analyzer.pipeline:process_codebase:99 - Step 4: Building knowledge graph...
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: f256175c-79df-4e02-b983-884ee785af76
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c5fbe4f1-8052-4ac6-81bb-04c9df14e058
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 4cf34c14-84df-49dd-93f3-a90cbd8b49ef
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 152c9988-5ea3-4ac1-838e-4df822ac24b3
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: c38a9110-a97e-49e5-94e9-2e30c32ce15e
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 102292c7-3a8c-495c-9927-2e7fae72e5ab
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: 04295f63-4f87-44ed-9cb9-2ebc880a1f95
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entity:385 - Added entity: d494766f-a616-488c-ae67-4640c613a249
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_entities:468 - Added 8 entities to knowledge graph
2025-07-18 11:10:49 | DEBUG    | rag2_analyzer.knowledge_graph.networkx_graph:add_relationships:478 - Added 0 relationships to knowledge graph
2025-07-18 11:10:49 | INFO     | rag2_analyzer.pipeline:process_codebase:107 - Codebase processing completed in 5.95 seconds
2025-07-18 11:10:53 | INFO     | rag2_analyzer.pipeline:query_codebase:167 - Processing query: what is project overview
2025-07-18 11:10:53 | DEBUG    | rag2_analyzer.pipeline:query_codebase:175 - Retrieving relevant context...
2025-07-18 11:10:53 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:32 - Processing and enhancing query...
2025-07-18 11:10:53 | DEBUG    | rag2_analyzer.retrieval.query_processor:process_query:44 - Processed query components: {'original_query': 'what is project overview', 'cleaned_query': 'what is project overview', 'keywords': ['what', 'project', 'overview'], 'code_entities': [{'type': 'variable', 'name': 'what'}, {'type': 'variable', 'name': 'project'}, {'type': 'variable', 'name': 'overview'}], 'intent': 'overview', 'language_hints': [], 'expanded_queries': ['what is project overview', 'main class', 'primary function', 'core functionality', 'key components', 'main method', 'application structure', 'what is project summary'], 'filters': {}}
2025-07-18 11:10:53 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:36 - Performing multi-query retrieval...
2025-07-18 11:10:53 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:10:57 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:10:59 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:02 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:05 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:07 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:10 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:13 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:16 | DEBUG    | rag2_analyzer.vector_db.qdrant_db:_generate_embeddings:231 - Using device: mps for embeddings
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:40 - Re-ranking retrieved chunks...
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 1: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1/push the box.py:70 - Score: 0.422 - {'vector_similarity': 0.86265093, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.6}
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 2: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1/push the box.py:83 - Score: 0.415 - {'vector_similarity': 0.85084414, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.5}
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:_rerank_chunks:140 - Rank 3: /private/var/folders/d7/5djnb9pn2l598k4_7y5ckpwc0000gp/T/tmpf1aa5af1/push the box.py:77 - Score: 0.412 - {'vector_similarity': 0.8275986, 'keyword_match': 0.0, 'entity_match': 0.0, 'intent_relevance': 0.5, 'language_match': 0.5, 'chunk_quality': 0.7999999999999999}
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:44 - Retrieving entities and relationships...
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:50 - Optimizing final context...
2025-07-18 11:11:19 | INFO     | rag2_analyzer.retrieval.advanced_retriever:retrieve_context:55 - Retrieved 4 chunks, 0 entities, 0 relationships
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:191 - Retrieved 4 chunks, 0 entities, 0 relationships
2025-07-18 11:11:19 | WARNING  | rag2_analyzer.pipeline:query_codebase:201 - No entities retrieved for query - this may indicate extraction issues
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:227 - Retrieved 4 chunks, 0 entities, 0 relationships
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:231 - Validating context quality...
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.utils.context_validator:validate_context:75 - Context validation: Quality=0.298, Valid=False, Issues=2
2025-07-18 11:11:19 | WARNING  | rag2_analyzer.pipeline:query_codebase:237 - Context quality issues detected: ['Chunk 2 is too long (2106 chars)', 'Chunk 4 is too long (1547 chars)']
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:240 - Context quality score: 0.298
2025-07-18 11:11:19 | DEBUG    | rag2_analyzer.pipeline:query_codebase:267 - Generating LLM response...
2025-07-18 11:11:21 | INFO     | rag2_analyzer.pipeline:query_codebase:291 - Query processed in 27.79 seconds
