"""Streamlit web interface for RAG  Code Analyzer."""

import streamlit as st
import asyncio
import os
import tempfile
from pathlib import Path
import sys
import time
import zipfile
import subprocess
import requests

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag2_analyzer.pipeline import CodeAnalysisPipeline
from rag2_analyzer.core.config import config
from rag2_analyzer.utils.logger import setup_logger

# Set up logging
setup_logger()

# Page configuration
st.set_page_config(
    page_title="RAG Code Analyzer",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'pipeline' not in st.session_state:
    st.session_state.pipeline = None
    st.session_state.initialized = False
    st.session_state.analysis_complete = False
    st.session_state.last_analysis_path = None


async def initialize_pipeline():
    """Initialize the pipeline."""
    if st.session_state.pipeline is None:
        st.session_state.pipeline = CodeAnalysisPipeline()
        await st.session_state.pipeline.initialize()
        st.session_state.initialized = True


def update_llm_provider(new_provider):
    """Update the LLM provider in .env file and force reload."""
    env_file = Path('.env')
    if not env_file.exists():
        st.error("❌ .env file not found!")
        return False

    try:
        # Read current content
        with open(env_file, 'r') as f:
            lines = f.readlines()

        # Update LLM_PROVIDER line
        updated = False
        for i, line in enumerate(lines):
            if line.startswith('LLM_PROVIDER='):
                lines[i] = f'LLM_PROVIDER={new_provider}\n'
                updated = True
                break

        if not updated:
            # Add LLM_PROVIDER if not found
            lines.insert(0, f'LLM_PROVIDER={new_provider}\n')

        # Write back
        with open(env_file, 'w') as f:
            f.writelines(lines)

        # Update environment variable for immediate effect
        os.environ['LLM_PROVIDER'] = new_provider

        # Force reload of config by clearing session state
        for key in list(st.session_state.keys()):
            if 'config' in key.lower() or 'pipeline' in key.lower():
                del st.session_state[key]

        # Reset pipeline initialization flag to force recreation
        st.session_state.initialized = False
        st.session_state.pipeline = None

        return True
    except Exception as e:
        st.error(f"❌ Error updating .env file: {e}")
        return False


def get_current_provider():
    """Get current provider from .env file directly (not cached config)."""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if line.startswith('LLM_PROVIDER='):
                    return line.split('=')[1].strip().lower()
    return "unknown"


def main():
    """Main Streamlit application."""

    # Header
    st.title("🔍 RAG Code Analyzer")
    st.markdown("*Intelligent code analysis and debugging with AI*")

    # Sidebar
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Show current configuration
        st.subheader("Current Settings")
        st.write(f"**Embedding Model:** {config.embedding_model}")
        st.write(f"**Vector DB:** {config.vector_db_type.upper()}")
        st.write(f"**Max Chunk Size:** {config.max_chunk_size}")
        st.write(f"**Log Level:** {config.log_level}")

        # Vector Database status
        if config.vector_db_type.lower() == "qdrant":
            try:
                import requests
                response = requests.get("http://localhost:6333/collections", timeout=2)
                if response.status_code == 200:
                    st.success("✅ Qdrant running")
                else:
                    st.error("❌ Qdrant not accessible")
            except:
                st.warning("⚠️ Qdrant not running")
                st.info("Run: python3 setup_qdrant.py")
        elif config.vector_db_type.lower() == "faiss":
            st.info("📦 FAISS (in-memory)")
        elif config.vector_db_type.lower() == "chroma":
            st.info("🔮 Chroma (persistent)")

        # LLM Provider status (read directly from .env)
        current_provider_display = get_current_provider()
        st.write(f"**LLM Provider:** {current_provider_display.upper()}")

        # Check provider status
        provider_status = {}

        # Check Gemini
        if config.gemini_api_key and config.gemini_api_key != "your_gemini_api_key_here":
            provider_status["Gemini"] = "✅"
        else:
            provider_status["Gemini"] = "❌"

        # Check Ollama
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                provider_status["Ollama"] = "✅"
            else:
                provider_status["Ollama"] = "❌"
        except:
            provider_status["Ollama"] = "❌"

        # Display current provider status (read directly from .env)
        current_provider_raw = get_current_provider()
        current_provider = current_provider_raw.title()
        if provider_status.get(current_provider) == "✅":
            st.success(f"✅ {current_provider} is working")
        else:
            st.error(f"❌ {current_provider} not available")

        # Show all provider status
        st.write("**All Providers:**")
        for provider, status in provider_status.items():
            st.write(f"  {status} {provider}")

        # LLM Provider Switching Section
        st.divider()
        st.subheader("🔄 Switch LLM Provider")

        # Show switching options with automatic switching
        col1, col2 = st.columns(2)

        with col1:
            if provider_status["Gemini"] == "✅" and current_provider_raw != "gemini":
                if st.button("🤖 Switch to Gemini", type="primary", key="switch_gemini"):
                    with st.spinner("Switching to Gemini..."):
                        if update_llm_provider("gemini"):
                            st.success("✅ Switched to Gemini!")
                            st.rerun()
            elif current_provider_raw == "gemini":
                st.success("🤖 Currently using Gemini")
            else:
                st.error("🤖 Gemini not available")

        with col2:
            if provider_status["Ollama"] == "✅" and current_provider_raw != "ollama":
                if st.button("🏠 Switch to Ollama", type="secondary", key="switch_ollama"):
                    with st.spinner("Switching to Ollama..."):
                        if update_llm_provider("ollama"):
                            st.success("✅ Switched to Ollama!")
                            st.rerun()
            elif current_provider_raw == "ollama":
                st.success("🏠 Currently using Ollama")
            else:
                st.error("🏠 Ollama not available")

        # Alternative method for advanced users
        st.info("💡 **For advanced users:** Use `python3 switch_llm.py` for command-line switching with testing")

        st.divider()

        # System status
        st.subheader("System Status")
        if st.session_state.initialized:
            st.success("✅ Pipeline initialized")
        else:
            st.info("🔄 Pipeline not initialized")

        if st.session_state.analysis_complete:
            st.success("✅ Analysis complete")
            if st.session_state.last_analysis_path:
                st.write(f"**Last analyzed:** {st.session_state.last_analysis_path}")
        else:
            st.info("📝 No analysis performed yet")

    # Main content tabs
    tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
        "📁 Analyze Code",
        "❓ Query Code",
        "📊 Statistics",
        "🕸️ Graph Viz",
        "🔍 Graph Queries",
        "🎯 Accuracy",
        "🧪 Examples"
    ])

    with tab1:
        st.header("Analyze Your Codebase")

        # Initialize pipeline button
        if not st.session_state.initialized:
            if st.button("🚀 Initialize System", type="primary"):
                with st.spinner("Initializing RAG  pipeline..."):
                    asyncio.run(initialize_pipeline())
                st.success("Pipeline initialized successfully!")
                st.rerun()

        if st.session_state.initialized:
            # File upload or path input
            analysis_method = st.radio(
                "Choose analysis method:",
                ["Upload Files", "Specify Path", "GitHub Repository"]
            )

            if analysis_method == "Upload Files":
                uploaded_files = st.file_uploader(
                    "Upload source code files or ZIP archives",
                    accept_multiple_files=True,
                    type=['py', 'java', 'js', 'ts', 'jsx', 'tsx', 'cpp', 'c', 'cs', 'go', 'rs', 'php', 'rb', 'swift',
                          'kt', 'scala', 'zip'],
                    help="Supports individual code files and ZIP archives containing codebases"
                )

                if not uploaded_files:
                    st.info("💡 **Supported formats:**")
                    st.write(
                        "📄 **Individual files:** .py, .java, .js, .ts, .jsx, .tsx, .cpp, .c, .cs, .go, .rs, .php, .rb, .swift, .kt, .scala")
                    st.write("📦 **ZIP archives:** Upload entire codebases as .zip files")
                    st.write("🔄 **Mixed uploads:** Combine individual files and ZIP archives")
                    st.write("🎯 **Perfect for:** GitHub repos, project folders, code libraries")

                # Option to clear previous data
                clear_previous = st.checkbox(
                    "🗑️ Clear previous analysis data",
                    value=True,
                    help="When checked, previous files and analysis will be cleared. Uncheck to add to existing analysis."
                )

                if uploaded_files and st.button("🔍 Analyze Uploaded Files"):
                    analyze_uploaded_files(uploaded_files, clear_previous)

            elif analysis_method == "Specify Path":
                code_path = st.text_input(
                    "Enter path to code file or directory:",
                    placeholder="/path/to/your/code"
                )

                if code_path and st.button("🔍 Analyze Path"):
                    if os.path.exists(code_path):
                        analyze_path(code_path)
                    else:
                        st.error(f"Path does not exist: {code_path}")

            else:  # GitHub Repository
                st.subheader("🐙 GitHub Repository Analysis")
                github_url = st.text_input(
                    "Enter GitHub repository or file URL:",
                    placeholder="https://github.com/username/repository",
                    help="Supports both repository URLs and file/directory URLs. Examples:\n• https://github.com/username/repository\n• https://github.com/username/repository/blob/main/src/file.py"
                )

                col1, col2 = st.columns(2)
                with col1:
                    use_git_clone = st.checkbox(
                        "Use git clone",
                        value=True,
                        help="Use git clone (requires git installed) or download ZIP"
                    )
                with col2:
                    clear_after_analysis = st.checkbox(
                        "Clear after analysis",
                        value=True,
                        help="Remove downloaded repository after analysis"
                    )

                if github_url and st.button("🔍 Analyze GitHub Repository"):
                    analyze_github_repository(github_url, use_git_clone, clear_after_analysis)

    with tab2:
        st.header("Query Your Codebase")

        if not st.session_state.analysis_complete:
            st.info("Please analyze some code first before querying.")
        else:
            # Query input
            query = st.text_area(
                "Enter your question about the code:",
                placeholder="What does the main function do?\nFind all database-related functions\nExplain the authentication logic",
                height=100
            )

            # Analysis type
            analysis_type = st.selectbox(
                "Analysis Type:",
                ["general", "explain", "debug", "impact", "similarity"],
                index=0,  # Default to general
                help="Choose the type of analysis to perform:\n• General: Project overview and general questions\n• Explain: Detailed code explanations\n• Debug: Find and fix issues\n• Impact: Analyze change impacts\n• Similarity: Find similar code patterns"
            )

            # Max results
            max_results = st.slider("Maximum results:", 1, 20, 10)

            # Filtering options
            st.subheader("🎯 Result Filtering")
            col1, col2 = st.columns(2)

            with col1:
                recent_files_only = st.checkbox(
                    "📄 Recent files only",
                    value=True,
                    help="Only show results from recently uploaded/analyzed files"
                )

            with col2:
                file_filter = st.text_input(
                    "🔍 Filter by filename:",
                    placeholder="e.g., main.py, utils",
                    help="Only show results from files containing this text"
                )

            if query and st.button("🤖 Ask AI"):
                query_codebase(query, analysis_type, max_results, file_filter, recent_files_only)

    with tab3:
        st.header("System Statistics")

        if st.session_state.initialized and st.session_state.analysis_complete:
            show_statistics()
        else:
            st.info("Statistics will be available after analyzing code.")

    with tab4:
        st.header("🕸️ Knowledge Graph Visualization")

        if st.session_state.initialized and st.session_state.analysis_complete:
            # Get entities and relationships
            entities = list(st.session_state.pipeline.knowledge_graph.entities.values())
            relationships = list(st.session_state.pipeline.knowledge_graph.relationships.values())

            if entities:
                # Import visualization module
                sys.path.insert(0, str(Path('./src')))
                from rag2_analyzer.visualization.graph_visualizer import GraphVisualizer

                visualizer = GraphVisualizer()

                # Visualization options
                viz_type = st.selectbox(
                    "Choose visualization type:",
                    ["File Hierarchy", "Class Dependencies", "Mermaid Diagram", "Graph Statistics", "Entity Explorer"]
                )

                if viz_type == "File Hierarchy":
                    st.subheader("📁 File & Folder Hierarchy")

                    # Import file hierarchy visualizer
                    from rag2_analyzer.visualization.file_hierarchy_visualizer import FileHierarchyVisualizer

                    file_viz = FileHierarchyVisualizer()

                    with st.spinner("Analyzing file hierarchy..."):
                        hierarchy_analysis = file_viz.analyze_file_hierarchy(entities)

                    # Display summary
                    summary = file_viz.get_hierarchy_summary(hierarchy_analysis)
                    st.text(summary)

                    # Display root folders
                    root_folders = hierarchy_analysis.get("root_folders", [])
                    if root_folders:
                        st.subheader("📊 Project Structure")

                        for root in root_folders:
                            with st.expander(f"📁 {root['name']} ({root['files_count']} files)"):
                                st.write(f"**Type:** {root['type']}")
                                st.write(f"**Path:** {root['full_path']}")
                                st.write(f"**Files:** {root['files_count']}")

                    # Display file tree structure
                    file_tree = hierarchy_analysis.get("file_tree", {})
                    if file_tree:
                        st.subheader("🌳 Directory Tree")

                        def display_tree(tree_data, level=0):
                            for name, data in tree_data.items():
                                indent = "  " * level
                                icon = "📁" if data["type"] == "folder" else "📄"
                                entity_count = len(data.get("entities", []))
                                entity_info = f" ({entity_count} entities)" if entity_count > 0 else ""

                                st.write(f"{indent}{icon} {name}{entity_info}")

                                if data["type"] == "folder" and data["children"] and level < 3:
                                    display_tree(data["children"], level + 1)

                        display_tree(file_tree)

                    # Generate hierarchy diagram
                    st.subheader("📊 File Hierarchy Diagram")
                    max_nodes = st.slider("Maximum nodes in diagram", 5, 30, 15, key="file_hierarchy_nodes")

                    if st.button("🎨 Generate File Hierarchy Diagram"):
                        with st.spinner("Generating file hierarchy diagram..."):
                            mermaid_code = file_viz.generate_hierarchy_mermaid(hierarchy_analysis, max_nodes)

                            st.code(mermaid_code, language="mermaid")
                            st.info(
                                "💡 Copy the code above and paste it into a Mermaid viewer like https://mermaid.live")

                    # Statistics
                    stats = hierarchy_analysis.get("statistics", {})
                    if stats:
                        st.subheader("📈 File Statistics")

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Total Files", stats.get("total_files", 0))
                            st.metric("Total Folders", stats.get("total_folders", 0))

                        with col2:
                            st.metric("Total Entities", stats.get("total_entities", 0))
                            st.metric("Max Depth", stats.get("max_depth", 0))

                        with col3:
                            st.metric("Avg Entities/File", f"{stats.get('average_entities_per_file', 0):.1f}")

                        # File types
                        file_extensions = stats.get("file_extensions", {})
                        if file_extensions:
                            st.write("**📋 File Types:**")
                            for ext, count in sorted(file_extensions.items(), key=lambda x: x[1], reverse=True):
                                st.write(f"• {ext}: {count} files")

                        # Files with most entities
                        most_entities = stats.get("files_with_most_entities", [])
                        if most_entities:
                            st.write("**🎯 Files with Most Entities:**")
                            for file_path, count in most_entities[:5]:
                                file_name = file_path.split('/')[-1]
                                st.write(f"• {file_name}: {count} entities")

                elif viz_type == "Class Dependencies":
                    st.subheader("🏗️ Class Dependency Hierarchy")

                    # Import class dependency visualizer
                    from rag2_analyzer.visualization.class_dependency_visualizer import ClassDependencyVisualizer

                    class_viz = ClassDependencyVisualizer()

                    with st.spinner("Analyzing class dependencies..."):
                        dependency_analysis = class_viz.analyze_class_dependencies(entities, relationships)

                    # Display summary
                    summary = class_viz.get_class_hierarchy_summary(dependency_analysis)
                    st.text(summary)

                    # Display root classes
                    root_classes = dependency_analysis.get("root_classes", [])
                    if root_classes:
                        st.subheader("📊 Root Classes (Entry Points)")

                        for root in root_classes:
                            with st.expander(f"🏛️ {root['name']}"):
                                st.write(f"**File:** {root['file']}")

                                # Show dependency tree for this root
                                tree = dependency_analysis["dependency_tree"].get(root['name'], {})
                                if tree:
                                    st.write("**Dependencies:**")
                                    for dep in tree.get("dependencies", []):
                                        st.write(f"  • {dep['name']} ({dep['relationship']})")

                                    st.write("**Dependents:**")
                                    for dep in tree.get("dependents", []):
                                        st.write(f"  • {dep['name']} ({dep['relationship']})")

                    # Generate dependency diagram
                    st.subheader("📊 Class Dependency Diagram")
                    max_nodes = st.slider("Maximum nodes in diagram", 5, 20, 10, key="class_dep_nodes")

                    if st.button("🎨 Generate Class Dependency Diagram"):
                        with st.spinner("Generating class dependency diagram..."):
                            mermaid_code = class_viz.generate_dependency_mermaid(dependency_analysis, max_nodes)

                            st.code(mermaid_code, language="mermaid")
                            st.info(
                                "💡 Copy the code above and paste it into a Mermaid viewer like https://mermaid.live")

                    # Statistics
                    stats = dependency_analysis.get("statistics", {})
                    if stats:
                        st.subheader("📈 Dependency Statistics")

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Total Classes", stats.get("total_classes", 0))
                            st.metric("Inheritance Relations", stats.get("inheritance_relationships", 0))

                        with col2:
                            st.metric("Usage Relations", stats.get("usage_relationships", 0))
                            st.metric("Contains Relations", stats.get("contains_relationships", 0))

                        with col3:
                            st.metric("Avg Dependencies", f"{stats.get('average_dependencies', 0):.1f}")
                            st.metric("Avg Dependents", f"{stats.get('average_dependents', 0):.1f}")

                        # Most connected classes
                        most_deps = stats.get("most_dependencies", [])
                        most_dependents = stats.get("most_dependents", [])

                        if most_deps or most_dependents:
                            col1, col2 = st.columns(2)

                            with col1:
                                if most_deps:
                                    st.write("**🔗 Most Dependencies:**")
                                    for class_name, count in most_deps:
                                        st.write(f"• {class_name}: {count}")

                            with col2:
                                if most_dependents:
                                    st.write("**⬅️ Most Dependents:**")
                                    for class_name, count in most_dependents:
                                        st.write(f"• {class_name}: {count}")

                elif viz_type == "Mermaid Diagram":
                    st.subheader("📊 Interactive Graph Diagram")

                    # Options for Mermaid
                    max_nodes = st.slider("Maximum nodes to display", 5, 50, 20)

                    if st.button("🎨 Generate Graph Visualization"):
                        with st.spinner("Generating graph visualization..."):
                            mermaid_code = visualizer.generate_mermaid_diagram(
                                entities, relationships, max_nodes
                            )

                            # Display the mermaid diagram
                            st.code(mermaid_code, language="mermaid")
                            st.info(
                                "💡 Copy the code above and paste it into a Mermaid viewer like https://mermaid.live")

                elif viz_type == "Graph Statistics":
                    st.subheader("📈 Detailed Graph Statistics")

                    graph_stats = visualizer.get_graph_statistics(entities, relationships)

                    # Display comprehensive stats
                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric("Total Entities", graph_stats["total_entities"])
                        st.metric("Total Relationships", graph_stats["total_relationships"])
                        st.metric("Files Analyzed", graph_stats["files_analyzed"])

                    with col2:
                        # Entity type distribution
                        if graph_stats["entity_types"]:
                            st.write("**Entity Types:**")
                            for entity_type, count in graph_stats["entity_types"].items():
                                st.write(f"• {entity_type}: {count}")

                        # Relationship type distribution
                        if graph_stats["relationship_types"]:
                            st.write("**Relationship Types:**")
                            for rel_type, count in graph_stats["relationship_types"].items():
                                st.write(f"• {rel_type}: {count}")

                    # Files analyzed
                    if graph_stats["files"]:
                        st.subheader("📁 Analyzed Files")
                        for file_path in graph_stats["files"][:10]:  # Show first 10
                            st.write(f"• {file_path}")
                        if len(graph_stats["files"]) > 10:
                            st.write(f"... and {len(graph_stats['files']) - 10} more files")

                elif viz_type == "Entity Explorer":
                    st.subheader("🔍 Entity Relationship Explorer")

                    # Entity search
                    entity_names = [e.name for e in entities]
                    selected_entity = st.selectbox(
                        "Select an entity to explore:",
                        [""] + entity_names
                    )

                    if selected_entity:
                        # Get entity neighbors
                        neighbors = st.session_state.pipeline.knowledge_graph.get_entity_neighbors(selected_entity)

                        st.write(f"**Relationships for: {selected_entity}**")

                        # Display relationships in columns
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write("**Outgoing Relationships:**")
                            if neighbors["inherits_from"]:
                                st.write("🔗 Inherits from:")
                                for entity in neighbors["inherits_from"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["calls"]:
                                st.write("📞 Calls:")
                                for entity in neighbors["calls"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["uses"]:
                                st.write("🔧 Uses:")
                                for entity in neighbors["uses"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["contains"]:
                                st.write("📦 Contains:")
                                for entity in neighbors["contains"]:
                                    st.write(f"  • {entity.name}")

                        with col2:
                            st.write("**Incoming Relationships:**")
                            if neighbors["inherited_by"]:
                                st.write("🔗 Inherited by:")
                                for entity in neighbors["inherited_by"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["called_by"]:
                                st.write("📞 Called by:")
                                for entity in neighbors["called_by"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["used_by"]:
                                st.write("🔧 Used by:")
                                for entity in neighbors["used_by"]:
                                    st.write(f"  • {entity.name}")

                            if neighbors["contained_by"]:
                                st.write("📦 Contained by:")
                                for entity in neighbors["contained_by"]:
                                    st.write(f"  • {entity.name}")

                        # Find related entities
                        st.subheader("🌐 Related Entities")
                        depth = st.slider("Search depth", 1, 3, 2)

                        if st.button("🔍 Find Related Entities"):
                            related = st.session_state.pipeline.knowledge_graph.find_related_entities(
                                selected_entity, depth
                            )

                            if related:
                                st.write(f"Found {len(related)} related entities:")
                                for entity in related[:20]:  # Show first 20
                                    st.write(f"• {entity.name} ({entity.entity_type.value}) - {entity.file_path}")
                            else:
                                st.write("No related entities found.")

            else:
                st.info("No entities found. Upload and analyze code first.")

        else:
            st.info("📝 Upload and analyze code to see knowledge graph visualization")

    with tab5:
        st.header("🔍 Graph-Based Queries")

        if st.session_state.initialized and st.session_state.analysis_complete:
            entities = list(st.session_state.pipeline.knowledge_graph.entities.values())

            if entities:
                query_type = st.selectbox(
                    "Choose query type:",
                    ["Entity Search", "Path Finding", "Relationship Analysis"]
                )

                if query_type == "Entity Search":
                    st.subheader("🔍 Search Entities")

                    search_term = st.text_input("Enter entity name to search:")

                    if search_term:
                        # Find matching entities
                        matching_entities = [
                            e for e in entities
                            if search_term.lower() in e.name.lower()
                        ]

                        if matching_entities:
                            st.write(f"Found {len(matching_entities)} matching entities:")

                            for entity in matching_entities[:10]:
                                with st.expander(f"{entity.name} ({entity.entity_type.value})"):
                                    st.write(f"**File:** {entity.file_path}")
                                    st.write(f"**Lines:** {entity.start_line}-{entity.end_line}")
                                    st.write(f"**Description:** {entity.description}")
                        else:
                            st.write("No matching entities found.")

                elif query_type == "Path Finding":
                    st.subheader("🛤️ Find Path Between Entities")

                    entity_names = [e.name for e in entities]

                    col1, col2 = st.columns(2)
                    with col1:
                        source_entity = st.selectbox("Source entity:", [""] + entity_names, key="source")
                    with col2:
                        target_entity = st.selectbox("Target entity:", [""] + entity_names, key="target")

                    if source_entity and target_entity and st.button("🔍 Find Path"):
                        path = st.session_state.pipeline.knowledge_graph.find_path_between_entities(
                            source_entity, target_entity
                        )

                        if path:
                            st.success(f"Found path with {len(path)} steps:")
                            for i, entity in enumerate(path):
                                if i < len(path) - 1:
                                    st.write(f"{i + 1}. {entity.name} ({entity.entity_type.value}) →")
                                else:
                                    st.write(f"{i + 1}. {entity.name} ({entity.entity_type.value})")
                        else:
                            st.warning("No path found between these entities.")

                elif query_type == "Relationship Analysis":
                    st.subheader("📊 Relationship Analysis")

                    relationships = list(st.session_state.pipeline.knowledge_graph.relationships.values())

                    if relationships:
                        # Relationship type distribution
                        rel_types = {}
                        for rel in relationships:
                            rel_type = rel.relationship_type.value
                            rel_types[rel_type] = rel_types.get(rel_type, 0) + 1

                        st.write("**Relationship Type Distribution:**")
                        for rel_type, count in rel_types.items():
                            st.write(f"• {rel_type}: {count}")

                        # Most connected entities
                        entity_connections = {}
                        for rel in relationships:
                            source_id = rel.source_entity_id
                            target_id = rel.target_entity_id

                            entity_connections[source_id] = entity_connections.get(source_id, 0) + 1
                            entity_connections[target_id] = entity_connections.get(target_id, 0) + 1

                        if entity_connections:
                            st.write("**Most Connected Entities:**")
                            sorted_entities = sorted(
                                entity_connections.items(),
                                key=lambda x: x[1],
                                reverse=True
                            )[:10]

                            for entity_id, connections in sorted_entities:
                                if entity_id in st.session_state.pipeline.knowledge_graph.entities:
                                    entity = st.session_state.pipeline.knowledge_graph.entities[entity_id]
                                    st.write(f"• {entity.name}: {connections} connections")
                    else:
                        st.info("No relationships found yet.")

            else:
                st.info("No entities found. Upload and analyze code first.")

        else:
            st.info("📝 Upload and analyze code to see graph-based queries")

    with tab6:
        st.header("🎯 System Analysis")

        if st.session_state.initialized and st.session_state.analysis_complete:
            st.subheader("📊 Analysis Summary")

            # Get basic statistics
            entities = list(st.session_state.pipeline.knowledge_graph.entities.values())
            relationships = list(st.session_state.pipeline.knowledge_graph.relationships.values())

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Entities", len(entities))
            with col2:
                st.metric("Total Relationships", len(relationships))
            with col3:
                # Handle both Entity objects and dictionaries
                if entities:
                    try:
                        if hasattr(entities[0], 'file_path'):
                            # Entity objects
                            files = list(set(e.file_path for e in entities))
                        else:
                            # Dictionary format
                            files = list(set(e.get('file_path', 'unknown') for e in entities))
                    except (AttributeError, KeyError):
                        files = []
                else:
                    files = []
                st.metric("Files Analyzed", len(files))

            # Entity type breakdown
            if entities:
                entity_types = {}
                for entity in entities:
                    try:
                        # Handle both Entity objects and dictionaries
                        if hasattr(entity, 'entity_type'):
                            entity_type = entity.entity_type.value
                        else:
                            entity_type = entity.get('entity_type', 'unknown')
                    except (AttributeError, KeyError):
                        entity_type = 'unknown'
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1

                st.subheader("📋 Entity Types")
                for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True):
                    st.write(f"• {entity_type}: {count}")
        else:
            st.info("📝 Upload and analyze code to see analysis summary")

    with tab7:
        st.header("Examples & Demo")
        show_examples()


def analyze_uploaded_files(uploaded_files, clear_previous=True):
    """Analyze uploaded files."""
    action_text = "Analyzing uploaded files (clearing previous data)..." if clear_previous else "Analyzing uploaded files (keeping previous data)..."

    with st.spinner(action_text):
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Process uploaded files (including ZIP extraction)
            zip_files = []
            regular_files = []

            # Separate ZIP files from regular files
            for uploaded_file in uploaded_files:
                if uploaded_file.name.lower().endswith('.zip'):
                    zip_files.append(uploaded_file)
                else:
                    regular_files.append(uploaded_file)

            # Save regular files to temporary directory
            for uploaded_file in regular_files:
                file_path = os.path.join(temp_dir, uploaded_file.name)
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())

            # Extract ZIP files
            for zip_file in zip_files:
                st.info(f"📦 Extracting ZIP file: {zip_file.name}")

                # Save ZIP file temporarily
                zip_path = os.path.join(temp_dir, zip_file.name)
                with open(zip_path, "wb") as f:
                    f.write(zip_file.getbuffer())

                # Extract ZIP contents
                try:
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # Create extraction directory
                        extract_dir = os.path.join(temp_dir, f"extracted_{zip_file.name[:-4]}")
                        os.makedirs(extract_dir, exist_ok=True)

                        # Extract all files
                        zip_ref.extractall(extract_dir)

                        # Count extracted files
                        extracted_files = []
                        supported_extensions = (
                            '.py', '.java', '.js', '.ts', '.jsx', '.tsx',
                            '.cpp', '.c', '.cs', '.go', '.rs', '.php',
                            '.rb', '.swift', '.kt', '.scala'
                        )

                        for root, dirs, files in os.walk(extract_dir):
                            for file in files:
                                if file.lower().endswith(supported_extensions):
                                    extracted_files.append(os.path.join(root, file))

                        if extracted_files:
                            st.success(f"✅ Extracted {len(extracted_files)} code files from {zip_file.name}")

                            # Show file types breakdown
                            file_types = {}
                            for file_path in extracted_files:
                                ext = Path(file_path).suffix.lower()
                                file_types[ext] = file_types.get(ext, 0) + 1

                            type_summary = ", ".join([f"{count} {ext}" for ext, count in file_types.items()])
                            st.info(f"📊 File types: {type_summary}")
                        else:
                            st.warning(f"⚠️ No supported code files found in {zip_file.name}")

                except zipfile.BadZipFile:
                    st.error(f"❌ Invalid ZIP file: {zip_file.name}")
                except Exception as e:
                    st.error(f"❌ Error extracting {zip_file.name}: {e}")
                finally:
                    # Remove the ZIP file to save space
                    if os.path.exists(zip_path):
                        os.remove(zip_path)

            # Analyze the temporary directory
            if clear_previous:
                status = asyncio.run(
                    st.session_state.pipeline.process_codebase_fresh(temp_dir, clear_previous=True)
                )
            else:
                status = asyncio.run(
                    st.session_state.pipeline.process_codebase(temp_dir)
                )

            # Update session state
            st.session_state.analysis_complete = True
            st.session_state.last_analysis_path = f"{len(uploaded_files)} uploaded files"

            # Show results
            st.success("Analysis completed successfully!")

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Files Processed", status.processed_files)
            with col2:
                st.metric("Code Chunks", status.total_chunks)
            with col3:
                st.metric("Entities", status.total_entities)
            with col4:
                st.metric("Relationships", status.total_relationships)

            if status.errors:
                st.warning(f"Encountered {len(status.errors)} errors during analysis")
                with st.expander("View Errors"):
                    for error in status.errors:
                        st.error(error)


def analyze_path(code_path):
    """Analyze code at specified path."""
    with st.spinner(f"Analyzing {code_path}..."):
        status = asyncio.run(
            st.session_state.pipeline.process_codebase(code_path)
        )

        # Update session state
        st.session_state.analysis_complete = True
        st.session_state.last_analysis_path = code_path

        # Show results
        st.success("Analysis completed successfully!")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Files Processed", status.processed_files)
        with col2:
            st.metric("Code Chunks", status.total_chunks)
        with col3:
            st.metric("Entities", status.total_entities)
        with col4:
            st.metric("Relationships", status.total_relationships)

        if status.errors:
            st.warning(f"Encountered {len(status.errors)} errors during analysis")
            with st.expander("View Errors"):
                for error in status.errors:
                    st.error(error)


def analyze_github_repository(github_url, use_git_clone=True, clear_after_analysis=True):
    """Analyze a GitHub repository."""

    # Validate GitHub URL
    if not github_url.startswith(('https://github.com/', 'http://github.com/', 'github.com/')):
        st.error("❌ Please enter a valid GitHub repository URL")
        return

    # Normalize URL
    if github_url.startswith('github.com/'):
        github_url = 'https://' + github_url

    # Extract repo info - handle both repository URLs and file URLs
    try:
        # Remove .git suffix if present (only from the end)
        clean_url = github_url
        if github_url.endswith('.git'):
            clean_url = github_url[:-4]

        # Remove the GitHub domain part
        path_part = clean_url.replace('https://github.com/', '').replace('http://github.com/', '')
        parts = path_part.split('/')

        if len(parts) < 2:
            st.error("❌ Invalid GitHub repository URL format")
            return

        username = parts[0]
        repo_name = parts[1]

        # Check if this is a file URL (contains /blob/, /tree/, /raw/, etc.)
        if len(parts) > 2 and parts[2] in ['blob', 'tree', 'raw', 'commit', 'releases', 'issues', 'pull', 'wiki',
                                           'projects', 'security', 'insights', 'settings']:
            # This is a file/directory URL, extract just the repo part
            repo_url = f"https://github.com/{username}/{repo_name}"

            if parts[2] == 'blob' and len(parts) >= 5:
                # File URL: https://github.com/username/repo/blob/branch/path/to/file
                branch = parts[3]
                file_path = '/'.join(parts[4:])
                st.info(f"📄 Detected file URL - will analyze entire repository: {username}/{repo_name}")
                st.info(f"🌿 Detected branch: {branch}")
                st.info(f"📁 File path: {file_path}")
            elif parts[2] == 'tree' and len(parts) >= 4:
                # Directory URL: https://github.com/username/repo/tree/branch/path/to/dir
                branch = parts[3]
                dir_path = '/'.join(parts[4:]) if len(parts) > 4 else "root"
                st.info(f"� Detected directory URL - will analyze entire repository: {username}/{repo_name}")
                st.info(f"🌿 Detected branch: {branch}")
                st.info(f"📂 Directory path: {dir_path}")
            else:
                st.info(f"🔗 Detected GitHub URL - will analyze repository: {username}/{repo_name}")

            # Use the clean repository URL
            github_url = repo_url
            clean_url = repo_url
        else:
            # This is a repository URL
            st.info(f"�📥 Preparing to analyze repository: {username}/{repo_name}")

    except Exception as e:
        st.error(f"❌ Error parsing GitHub URL: {e}")
        return

    # Create temporary directory for cloning/downloading
    with tempfile.TemporaryDirectory() as temp_dir:
        repo_path = os.path.join(temp_dir, repo_name)

        try:
            if use_git_clone:
                # Try git clone first
                st.info("🔄 Cloning repository with git...")
                result = subprocess.run(
                    ['git', 'clone', github_url, repo_path],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )

                if result.returncode != 0:
                    st.warning("⚠️ Git clone failed, falling back to ZIP download...")
                    use_git_clone = False
                else:
                    st.success("✅ Repository cloned successfully!")

            if not use_git_clone:
                # Download as ZIP
                st.info("📦 Downloading repository as ZIP...")
                zip_url = f"{clean_url}/archive/refs/heads/main.zip"

                # Try main branch first, then master
                for branch in ['main', 'master']:
                    zip_url = f"{clean_url}/archive/refs/heads/{branch}.zip"
                    response = requests.get(zip_url, timeout=60)

                    if response.status_code == 200:
                        # Save and extract ZIP
                        zip_path = os.path.join(temp_dir, f"{repo_name}.zip")
                        with open(zip_path, 'wb') as f:
                            f.write(response.content)

                        # Extract ZIP
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(temp_dir)

                        # Find extracted directory (usually repo-name-branch)
                        extracted_dirs = [d for d in os.listdir(temp_dir) if
                                          os.path.isdir(os.path.join(temp_dir, d)) and d != repo_name]
                        if extracted_dirs:
                            # Rename to expected repo name
                            os.rename(os.path.join(temp_dir, extracted_dirs[0]), repo_path)

                        st.success("✅ Repository downloaded successfully!")
                        break
                else:
                    st.error("❌ Could not download repository. Please check the URL and try again.")
                    return

            # Check if repository was successfully obtained
            if not os.path.exists(repo_path):
                st.error("❌ Failed to obtain repository")
                return

            # Count code files
            code_files = []
            supported_extensions = (
                '.py', '.java', '.js', '.ts', '.jsx', '.tsx',
                '.cpp', '.c', '.cs', '.go', '.rs', '.php',
                '.rb', '.swift', '.kt', '.scala'
            )

            for root, dirs, files in os.walk(repo_path):
                # Skip common non-code directories
                dirs[:] = [d for d in dirs if
                           not d.startswith('.') and d not in ['node_modules', '__pycache__', 'target', 'build',
                                                               'dist']]

                for file in files:
                    if file.lower().endswith(supported_extensions):
                        code_files.append(os.path.join(root, file))

            if not code_files:
                st.warning("⚠️ No supported code files found in repository")
                return

            st.info(f"📊 Found {len(code_files)} code files to analyze")

            # Analyze the repository
            with st.spinner(f"Analyzing {username}/{repo_name}..."):
                status = asyncio.run(
                    st.session_state.pipeline.process_codebase(repo_path)
                )

                # Update session state
                st.session_state.analysis_complete = True
                st.session_state.last_analysis_path = f"{username}/{repo_name} (GitHub)"

                # Show results
                st.success("✅ GitHub repository analysis completed!")

                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Files Processed", status.processed_files)
                with col2:
                    st.metric("Code Chunks", status.total_chunks)
                with col3:
                    st.metric("Entities", status.total_entities)
                with col4:
                    st.metric("Relationships", status.total_relationships)

                if status.errors:
                    st.warning(f"Encountered {len(status.errors)} errors during analysis")
                    with st.expander("View Errors"):
                        for error in status.errors:
                            st.error(error)

        except subprocess.TimeoutExpired:
            st.error("❌ Repository cloning timed out. Repository might be too large.")
        except requests.RequestException as e:
            st.error(f"❌ Network error: {e}")
        except Exception as e:
            st.error(f"❌ Error analyzing GitHub repository: {e}")


def query_codebase(query, analysis_type, max_results, file_filter=None, recent_files_only=False):
    """Query the analyzed codebase."""
    with st.spinner("Processing your query..."):
        result = asyncio.run(
            st.session_state.pipeline.query_codebase(
                query=query,
                analysis_type=analysis_type,
                max_results=max_results,
                file_filter=file_filter if file_filter else None,
                recent_files_only=recent_files_only
            )
        )

        # Show results
        st.subheader("🤖 AI Response")
        st.write(result.llm_response)

        # Show relevant code chunks
        if result.relevant_chunks:
            st.subheader("📄 Relevant Code")
            for i, chunk in enumerate(result.relevant_chunks[:3]):  # Show top 3
                with st.expander(f"Chunk {i + 1}: {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line})"):
                    st.code(chunk.content, language=chunk.language.value)

        # Show relevant entities
        if result.relevant_entities:
            st.subheader("🏗️ Relevant Entities")
            for entity in result.relevant_entities[:5]:  # Show top 5
                st.write(f"**{entity.name}** ({entity.entity_type.value}) - {entity.file_path}:{entity.start_line}")


def show_statistics():
    """Show system statistics."""
    try:
        # Get knowledge graph stats
        graph_stats = st.session_state.pipeline.knowledge_graph.get_graph_stats()

        st.subheader("📊 Knowledge Graph Statistics")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Entities", graph_stats['num_entities'])
        with col2:
            st.metric("Total Relationships", graph_stats['num_relationships'])
        with col3:
            st.metric("Graph Nodes", graph_stats['num_nodes'])
        with col4:
            st.metric("Graph Edges", graph_stats['num_edges'])

        # Entity types breakdown
        if graph_stats['entity_types']:
            st.subheader("Entity Types Distribution")
            entity_data = graph_stats['entity_types']
            st.bar_chart(entity_data)

        # Vector database stats
        st.subheader("📦 Vector Database Statistics")

        # Get Qdrant collection info
        try:
            collection_info = st.session_state.pipeline.vector_db.get_collection_info()

            col1, col2, col3 = st.columns(3)
            with col1:
                points_count = collection_info.get('points_count', 0)
                st.metric("Total Points", points_count)

            with col2:
                vectors_count = collection_info.get('vectors_count', 0) or 0
                st.metric("Total Vectors", vectors_count)

            with col3:
                vector_dimension = collection_info.get('vector_dimension', 0)
                st.metric("Vector Dimension", vector_dimension)

            # Collection status
            status = collection_info.get('status', 'unknown')
            if hasattr(status, 'value'):
                status = status.value

            if status == 'green':
                st.success(f"✅ Collection Status: {status}")
            else:
                st.warning(f"⚠️ Collection Status: {status}")

        except Exception as ve:
            st.warning(f"Could not retrieve vector database stats: {ve}")
            st.metric("Total Vectors", "Unknown")

    except Exception as e:
        st.error(f"Error retrieving statistics: {e}")


def show_examples():
    """Show examples and demo content."""
    st.subheader("🧪 Try These Example Queries")

    example_queries = [
        ("What does this code do?", "general"),
        ("Find potential bugs in the authentication logic", "debug"),
        ("Explain how the database connection works", "explain"),
        ("What would be the impact of changing the User class?", "impact"),
        ("Find code similar to: def process_data(data):", "similarity")
    ]

    for query, analysis_type in example_queries:
        if st.button(f"Try: {query}", key=f"example_{hash(query)}"):
            if st.session_state.analysis_complete:
                query_codebase(query, analysis_type, 5)
            else:
                st.warning("Please analyze some code first!")

    st.divider()

    st.subheader("📝 Sample Code for Testing")
    st.write("You can copy this sample code and upload it as a .py file to test the system:")

    sample_code = '''
"""Sample Python module for testing RAG 2.0 Code Analyzer."""

class UserManager:
    """Manages user operations."""

    def __init__(self, database):
        self.db = database
        self.cache = {}

    def create_user(self, username, email):
        """Create a new user."""
        if self.get_user(username):
            raise ValueError("User already exists")

        user_data = {
            'username': username,
            'email': email,
            'created_at': 'now()'  # datetime.now()
        }

        user_id = self.db.insert('users', user_data)
        self.cache[username] = user_id
        return user_id

    def get_user(self, username):
        """Get user by username."""
        if username in self.cache:
            return self.db.get('users', self.cache[username])

        user = self.db.query('users', {'username': username})
        if user:
            self.cache[username] = user['id']
        return user

def authenticate_user(username, password):
    """Authenticate a user."""
    user = UserManager.get_user(username)
    if not user:
        return False

    return verify_password(password, user['password_hash'])
'''

    st.code(sample_code, language='python')


if __name__ == "__main__":
    main()
