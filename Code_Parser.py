#!/usr/bin/env python3
"""
Code Parser using Tree-sitter for extracting classes, functions, and relationships from source code.
"""

import os
import logging
from typing import Dict, List, Tuple, Set, Any, Optional
from pathlib import Path
from dataclasses import dataclass, field
from tree_sitter import Language, Parser
import tree_sitter_languages

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class CodeEntity:
    """Represents a code entity (class, function, method, etc.)"""
    name: str
    type: str
    file_path: str
    start_line: int
    end_line: int
    content: str
    parent: Optional[str] = None
    children: List[str] = field(default_factory=list)
    calls: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    inherits_from: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "type": self.type,
            "file_path": self.file_path,
            "start_line": self.start_line,
            "end_line": self.end_line,
            "content": self.content,
            "parent": self.parent,
            "children": self.children,
            "calls": self.calls,
            "imports": self.imports,
            "inherits_from": self.inherits_from
        }


class CodeParser:
    LANGUAGE_EXTENSIONS = {
        'python': ['.py'],
        'javascript': ['.js', '.jsx'],
        'typescript': ['.ts', '.tsx'],
        'java': ['.java'],
        'cpp': ['.cpp', '.cc', '.cxx', '.h', '.hpp'],
        'c': ['.c', '.h'],
        'c_sharp': ['.cs'],
        'go': ['.go'],
        'rust': ['.rs']
    }

    def __init__(self):
        self.parsers = {}
        self._init_parsers()

    def _init_parsers(self):
        for lang in self.LANGUAGE_EXTENSIONS.keys():
            try:
                ts_language = getattr(tree_sitter_languages, lang)
                parser = Parser()
                parser.set_language(ts_language)
                self.parsers[lang] = parser
                logger.info(f"Initialized parser for {lang}")
            except Exception as e:
                logger.warning(f"Failed to initialize parser for {lang}: {e}")

    def _get_language_from_file(self, file_path: str) -> Optional[str]:
        ext = os.path.splitext(file_path)[1].lower()
        for lang, extensions in self.LANGUAGE_EXTENSIONS.items():
            if ext in extensions:
                return lang
        return None

    def _extract_code_content(self, file_path: str, start_byte: int, end_byte: int) -> str:
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            return content[start_byte:end_byte].decode('utf-8', errors='replace')
        except Exception as e:
            logger.error(f"Error extracting code content from {file_path}: {e}")
            return ""

    def _get_line_number(self, content: bytes, byte_offset: int) -> int:
        return content[:byte_offset].count(b'\n') + 1

    def _extract_imports(self, node, content: bytes) -> List[str]:
        imports = []
        if node.type == 'import_statement' or node.type == 'import_from_statement':
            for child in node.children:
                if child.type == 'dotted_name':
                    imports.append(content[child.start_byte:child.end_byte].decode('utf-8'))
        return imports

    def _extract_calls(self, node, content: bytes) -> List[str]:
        calls = []
        if node.type == 'call':
            for child in node.children:
                if child.type == 'identifier':
                    calls.append(content[child.start_byte:child.end_byte].decode('utf-8'))
        return calls

    def _parse_python(self, file_path: str, content: bytes, tree) -> List[CodeEntity]:
        entities = []
        imports = []
        current_class = None

        def traverse_node(node, parent_name=None):
            nonlocal entities, imports, current_class

            start_line = self._get_line_number(content, node.start_byte)
            end_line = self._get_line_number(content, node.end_byte)
            node_content = self._extract_code_content(file_path, node.start_byte, node.end_byte)

            if node.type == 'ERROR':
                logger.warning(f"Syntax error at lines {start_line}-{end_line} in {file_path}: {node_content}")
                # Create a chunk for the erroneous code
                entities.append(CodeEntity(
                    name=f"error_chunk_{start_line}",
                    type="error",
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content=node_content,
                    parent=parent_name
                ))
                return

            if node.type == 'class_definition':
                name_node = node.child_by_field_name('name')
                name = content[name_node.start_byte:name_node.end_byte].decode(
                    'utf-8') if name_node else 'unnamed_class'
                current_class = name
                inherits_from = []
                superclasses = node.child_by_field_name('superclasses')
                if superclasses:
                    for arg in superclasses.children:
                        if arg.type == 'identifier':
                            inherits_from.append(content[arg.start_byte:arg.end_byte].decode('utf-8'))

                entities.append(CodeEntity(
                    name=name,
                    type='class',
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content=node_content,
                    inherits_from=inherits_from
                ))

            elif node.type == 'function_definition':
                name_node = node.child_by_field_name('name')
                name = content[name_node.start_byte:name_node.end_byte].decode(
                    'utf-8') if name_node else 'unnamed_function'
                entity_type = 'method' if current_class else 'function'
                calls = []

                # Extract calls from function body
                body = node.child_by_field_name('body')
                if body:
                    for child in body.children:
                        calls.extend(self._extract_calls(child, content))

                entities.append(CodeEntity(
                    name=name,
                    type=entity_type,
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content=node_content,
                    parent=current_class,
                    calls=calls
                ))

            elif node.type in ('import_statement', 'import_from_statement'):
                imports.extend(self._extract_imports(node, content))

            for child in node.children:
                traverse_node(child, current_class)

        traverse_node(tree.root_node)

        # Add imports to relevant entities
        for entity in entities:
            if entity.type in ('class', 'function', 'method'):
                entity.imports = imports

        logger.info(f"Extracted {len(entities)} entities from {file_path}")
        return entities

    def parse_file(self, file_path: str) -> List[CodeEntity]:
        entities = []
        lang = self._get_language_from_file(file_path)

        if not lang or lang not in self.parsers:
            logger.warning(f"Unsupported language for file: {file_path}")
            return entities

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            parser = self.parsers[lang]
            tree = parser.parse(content)

            if lang == 'python':
                entities = self._parse_python(file_path, content, tree)
            else:
                # Placeholder for other languages
                logger.warning(f"Parsing not implemented for {lang}")

            return entities

        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {e}")
            return []

    def parse_directory(self, directory_path: str, recursive: bool = True) -> Dict[str, List[CodeEntity]]:
        results = {}

        try:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    lang = self._get_language_from_file(file_path)

                    if lang and lang in self.parsers:
                        entities = self.parse_file(file_path)
                        if entities:
                            results[file_path] = entities

                if not recursive:
                    break

            return results

        except Exception as e:
            logger.error(f"Error parsing directory {directory_path}: {e}")
            return {}


if __name__ == "__main__":
    parser = CodeParser()
    entities = parser.parse_file("example.py")
    for entity in entities:
        print(f"{entity.type}: {entity.name} (lines {entity.start_line}-{entity.end_line})")