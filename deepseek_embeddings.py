#!/usr/bin/env python3
"""
DeepSeek-Coder Embedding System for specialized code embeddings at function/class level.
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import torch
from transformers import <PERSON>Tokenizer, AutoModel
from sentence_transformers import SentenceTransformer
import hashlib
import pickle
import os

from code_parser import CodeEntity

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class CodeEmbedding:
    """Represents an embedding for a code entity"""
    entity_id: str
    entity_name: str
    entity_type: str
    file_path: str
    embedding: np.ndarray
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage (excluding embedding)"""
        return {
            "entity_id": self.entity_id,
            "entity_name": self.entity_name,
            "entity_type": self.entity_type,
            "file_path": self.file_path,
            "metadata": self.metadata
        }


class DeepSeekEmbedder:
    """
    DeepSeek-Coder embedding system for generating specialized code embeddings.
    """

    # Model configurations
    DEEPSEEK_MODELS = {
        "deepseek-coder-1.3b": "deepseek-ai/deepseek-coder-1.3b-base",
        "deepseek-coder-6.7b": "deepseek-ai/deepseek-coder-6.7b-base",
        "deepseek-coder-33b": "deepseek-ai/deepseek-coder-33b-base"
    }

    FALLBACK_MODELS = {
        "codet5": "Salesforce/codet5-base",
        "codebert": "microsoft/codebert-base",
        "unixcoder": "microsoft/unixcoder-base"
    }

    def __init__(self,
                 model_name: str = "deepseek-coder-1.3b",
                 cache_dir: str = ".embedding_cache",
                 device: str = "auto"):
        """
        Initialize the DeepSeek embedder

        Args:
            model_name: Name of the model to use
            cache_dir: Directory for caching embeddings
            device: Device to run the model on ('cpu', 'cuda', or 'auto')
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.device = self._get_device(device)

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Initialize model and tokenizer
        self.model = None
        self.tokenizer = None
        self.sentence_transformer = None

        self._load_model()

    def _get_device(self, device: str) -> str:
        """Determine the best device to use"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device

    def _load_model(self):
        """Load the embedding model"""
        try:
            # Try to load DeepSeek-Coder model
            if self.model_name in self.DEEPSEEK_MODELS:
                model_path = self.DEEPSEEK_MODELS[self.model_name]
                logger.info(f"Loading DeepSeek-Coder model: {model_path}")

                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
                self.model = AutoModel.from_pretrained(model_path)
                self.model.to(self.device)
                self.model.eval()

            else:
                # Fallback to sentence-transformers for easier usage
                logger.info(f"Using sentence-transformer fallback model")
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')

        except Exception as e:
            logger.error(f"Failed to load model {self.model_name}: {e}")
            logger.info("Falling back to sentence-transformers")
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')

    def _get_cache_key(self, content: str) -> str:
        """Generate cache key for content"""
        return hashlib.md5(content.encode()).hexdigest()

    def _load_from_cache(self, cache_key: str) -> Optional[np.ndarray]:
        """Load embedding from cache"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"Failed to load from cache: {e}")
        return None

    def _save_to_cache(self, cache_key: str, embedding: np.ndarray):
        """Save embedding to cache"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(embedding, f)
        except Exception as e:
            logger.warning(f"Failed to save to cache: {e}")

    def _preprocess_code(self, code: str, entity_type: str) -> str:
        """
        Preprocess code for better embedding quality

        Args:
            code: Raw code content
            entity_type: Type of entity (class, function, method)

        Returns:
            Preprocessed code
        """
        # Remove excessive whitespace
        lines = [line.strip() for line in code.split('\n') if line.strip()]

        # Add context based on entity type
        if entity_type == "class":
            prefix = "# Class definition:\n"
        elif entity_type == "function":
            prefix = "# Function definition:\n"
        elif entity_type == "method":
            prefix = "# Method definition:\n"
        else:
            prefix = "# Code:\n"

        return prefix + '\n'.join(lines)

    def embed_code(self, code: str, entity_type: str = "code") -> np.ndarray:
        """
        Generate embedding for code content

        Args:
            code: Code content to embed
            entity_type: Type of code entity

        Returns:
            Embedding vector as numpy array
        """
        # Preprocess code
        processed_code = self._preprocess_code(code, entity_type)

        # Check cache
        cache_key = self._get_cache_key(processed_code)
        cached_embedding = self._load_from_cache(cache_key)
        if cached_embedding is not None:
            return cached_embedding

        try:
            if self.model and self.tokenizer:
                # Use DeepSeek-Coder model
                embedding = self._embed_with_deepseek(processed_code)
            else:
                # Use sentence-transformer fallback
                embedding = self._embed_with_sentence_transformer(processed_code)

            # Cache the embedding
            self._save_to_cache(cache_key, embedding)
            return embedding

        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            # Return zero vector as fallback
            return np.zeros(384)  # Default dimension

    def _embed_with_deepseek(self, code: str) -> np.ndarray:
        """Generate embedding using DeepSeek-Coder model"""
        with torch.no_grad():
            # Tokenize
            inputs = self.tokenizer(
                code,
                return_tensors="pt",
                truncation=True,
                max_length=512,
                padding=True
            ).to(self.device)

            # Get embeddings
            outputs = self.model(**inputs)

            # Use mean pooling of last hidden states
            embeddings = outputs.last_hidden_state
            attention_mask = inputs['attention_mask']

            # Mean pooling
            masked_embeddings = embeddings * attention_mask.unsqueeze(-1)
            summed = torch.sum(masked_embeddings, dim=1)
            counts = torch.sum(attention_mask, dim=1, keepdim=True)
            mean_pooled = summed / counts

            return mean_pooled.cpu().numpy().flatten()

    def _embed_with_sentence_transformer(self, code: str) -> np.ndarray:
        """Generate embedding using sentence-transformer fallback"""
        return self.sentence_transformer.encode(code)

    def embed_entities(self, entities: List[CodeEntity]) -> List[CodeEmbedding]:
        """
        Generate embeddings for a list of code entities

        Args:
            entities: List of CodeEntity objects

        Returns:
            List of CodeEmbedding objects
        """
        embeddings = []

        for entity in entities:
            try:
                # Generate embedding
                embedding_vector = self.embed_code(entity.content, entity.type)

                # Create embedding object
                entity_id = f"{entity.file_path}:{entity.name}:{entity.start_line}"
                code_embedding = CodeEmbedding(
                    entity_id=entity_id,
                    entity_name=entity.name,
                    entity_type=entity.type,
                    file_path=entity.file_path,
                    embedding=embedding_vector,
                    metadata={
                        "start_line": entity.start_line,
                        "end_line": entity.end_line,
                        "parent": entity.parent,
                        "calls": entity.calls,
                        "imports": entity.imports,
                        "inherits_from": entity.inherits_from
                    }
                )

                embeddings.append(code_embedding)

            except Exception as e:
                logger.error(f"Error embedding entity {entity.name}: {e}")
                continue

        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings

    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Compute cosine similarity between two embeddings"""
        try:
            # Normalize vectors
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            # Compute cosine similarity
            similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
            return float(similarity)

        except Exception as e:
            logger.error(f"Error computing similarity: {e}")
            return 0.0


# Example usage
if __name__ == "__main__":
    embedder = DeepSeekEmbedder()

    # Test embedding
    sample_code = """
    def fibonacci(n):
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)
    """

    embedding = embedder.embed_code(sample_code, "function")
    print(f"Generated embedding with shape: {embedding.shape}")
    print(f"Embedding preview: {embedding[:5]}")
