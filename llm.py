#!/usr/bin/env python3
"""
LLM Integration for Ollama (local) and Gemini Flash 2.5 (cloud).
"""

import logging
import os
import time
import json
import requests
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass
import subprocess

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """Supported LLM providers"""
    OLLAMA = "ollama"
    GEMINI = "gemini"


@dataclass
class LLMResponse:
    """Response from LLM"""
    text: str
    tokens_used: int
    processing_time: float
    model_name: str
    provider: LLMProvider
    metadata: Dict[str, Any]


class LLMClient:
    """
    LLM client for Ollama (local) and Gemini Flash 2.5 (cloud).
    """

    # Ollama model configurations
    OLLAMA_MODELS = {
        "mistral": {
            "name": "mistral",
            "description": "Mistral 7B - Balanced performance and quality",
            "context_length": 8192,
            "recommended_for": ["general", "code", "chat"]
        },
        "llama3": {
            "name": "llama3",
            "description": "Llama 3 - Meta's latest model",
            "context_length": 8192,
            "recommended_for": ["general", "code", "chat"]
        },
        "codellama": {
            "name": "codellama",
            "description": "CodeLlama - Specialized for code",
            "context_length": 16384,
            "recommended_for": ["code"]
        },
        "deepseek-coder": {
            "name": "deepseek-coder",
            "description": "DeepSeek Coder - Specialized for code",
            "context_length": 16384,
            "recommended_for": ["code"]
        }
    }

    def __init__(self,
                 provider: LLMProvider = LLMProvider.OLLAMA,
                 api_key: Optional[str] = None,
                 model_name: str = "codellama",
                 ollama_base_url: str = "http://localhost:11434"):
        """
        Initialize LLM client

        Args:
            provider: LLM provider (OLLAMA or GEMINI)
            api_key: API key for cloud providers
            model_name: Model name to use
            ollama_base_url: Base URL for Ollama API
        """
        self.provider = provider
        self.api_key = api_key
        self.model_name = model_name
        self.ollama_base_url = ollama_base_url

        # Validate configuration
        self._validate_config()

    def _validate_config(self):
        """Validate client configuration"""
        if self.provider == LLMProvider.GEMINI and not self.api_key:
            logger.warning("Gemini requires an API key. Set GOOGLE_API_KEY environment variable or provide api_key.")
            self.api_key = os.environ.get("GOOGLE_API_KEY")

        if self.provider == LLMProvider.OLLAMA:
            # Check if Ollama is running
            try:
                response = requests.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code != 200:
                    logger.warning(f"Ollama server not responding at {self.ollama_base_url}")
            except Exception as e:
                logger.warning(f"Failed to connect to Ollama: {e}")

    def generate(self,
                 prompt: str,
                 max_tokens: int = 2048,
                 temperature: float = 0.7,
                 system_prompt: Optional[str] = None) -> str:
        """
        Generate text using the configured LLM

        Args:
            prompt: User prompt
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            system_prompt: Optional system prompt

        Returns:
            Generated text
        """
        try:
            response = self._generate_with_provider(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                system_prompt=system_prompt
            )

            return response.text

        except Exception as e:
            logger.error(f"Error generating text: {e}")
            return f"Error generating response: {str(e)}"

    def _generate_with_provider(self,
                                prompt: str,
                                max_tokens: int,
                                temperature: float,
                                system_prompt: Optional[str]) -> LLMResponse:
        """Generate text with the configured provider"""
        start_time = time.time()

        if self.provider == LLMProvider.OLLAMA:
            return self._generate_with_ollama(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                system_prompt=system_prompt
            )

        elif self.provider == LLMProvider.GEMINI:
            return self._generate_with_gemini(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                system_prompt=system_prompt
            )

        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

    def _generate_with_ollama(self,
                              prompt: str,
                              max_tokens: int,
                              temperature: float,
                              system_prompt: Optional[str]) -> LLMResponse:
        """Generate text using Ollama"""
        start_time = time.time()

        try:
            # Prepare request
            request_data = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }

            if system_prompt:
                request_data["system"] = system_prompt

            # Send request to Ollama
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=request_data
            )

            if response.status_code != 200:
                raise Exception(f"Ollama API error: {response.status_code} - {response.text}")

            # Parse response
            result = response.json()

            return LLMResponse(
                text=result.get("response", ""),
                tokens_used=result.get("eval_count", 0),
                processing_time=time.time() - start_time,
                model_name=self.model_name,
                provider=LLMProvider.OLLAMA,
                metadata={
                    "total_duration": result.get("total_duration", 0),
                    "load_duration": result.get("load_duration", 0),
                    "prompt_eval_count": result.get("prompt_eval_count", 0)
                }
            )

        except Exception as e:
            logger.error(f"Error generating with Ollama: {e}")
            return LLMResponse(
                text=f"Error generating with Ollama: {str(e)}",
                tokens_used=0,
                processing_time=time.time() - start_time,
                model_name=self.model_name,
                provider=LLMProvider.OLLAMA,
                metadata={"error": str(e)}
            )

    def _generate_with_gemini(self,
                              prompt: str,
                              max_tokens: int,
                              temperature: float,
                              system_prompt: Optional[str]) -> LLMResponse:
        """Generate text using Gemini Flash 2.5"""
        start_time = time.time()

        try:
            # Import Gemini SDK
            try:
                import google.generativeai as genai
            except ImportError:
                raise ImportError(
                    "google-generativeai package not installed. Install with: pip install google-generativeai")

            # Configure API key
            genai.configure(api_key=self.api_key)

            # Prepare model configuration
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 0.95,
                "top_k": 40
            }

            # Create model - use the correct model name
            model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",  # Updated to correct model name
                generation_config=generation_config
            )

            # Prepare prompt
            if system_prompt:
                chat = model.start_chat(history=[
                    {"role": "user", "parts": [system_prompt]},
                    {"role": "model", "parts": ["I'll follow those instructions."]}
                ])
                response = chat.send_message(prompt)
            else:
                response = model.generate_content(prompt)

            # Get response text
            response_text = response.text

            return LLMResponse(
                text=response_text,
                tokens_used=0,  # Gemini doesn't provide token count
                processing_time=time.time() - start_time,
                model_name="gemini-1.5-flash",
                provider=LLMProvider.GEMINI,
                metadata={
                    "prompt_feedback": getattr(response, "prompt_feedback", None),
                    "candidates": len(getattr(response, "candidates", []))
                }
            )

        except Exception as e:
            logger.error(f"Error generating with Gemini: {e}")
            return LLMResponse(
                text=f"Error generating with Gemini: {str(e)}",
                tokens_used=0,
                processing_time=time.time() - start_time,
                model_name="gemini-1.5-flash",
                provider=LLMProvider.GEMINI,
                metadata={"error": str(e)}
            )

    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get available models for the configured provider"""
        if self.provider == LLMProvider.OLLAMA:
            return self._get_ollama_models()
        elif self.provider == LLMProvider.GEMINI:
            return [
                {
                    "name": "gemini-1.5-flash",
                    "description": "Gemini 1.5 Flash - Google's fastest model",
                    "context_length": 32768,
                    "recommended_for": ["general", "code", "chat"]
                }
            ]
        else:
            return []

    def _get_ollama_models(self) -> List[Dict[str, Any]]:
        """Get available Ollama models"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code != 200:
                logger.warning(f"Failed to get Ollama models: {response.status_code}")
                return []

            models = response.json().get("models", [])
            return [
                {
                    "name": model.get("name"),
                    "description": model.get("details", {}).get("description", ""),
                    "size": model.get("details", {}).get("size", 0),
                    "modified_at": model.get("details", {}).get("modified_at", "")
                }
                for model in models
            ]

        except Exception as e:
            logger.error(f"Error getting Ollama models: {e}")
            return []

    def install_ollama_model(self, model_name: str) -> Dict[str, Any]:
        """
        Install an Ollama model

        Args:
            model_name: Name of the model to install

        Returns:
            Status dictionary
        """
        if self.provider != LLMProvider.OLLAMA:
            return {"success": False, "error": "Not using Ollama provider"}

        try:
            # Check if Ollama is running
            try:
                response = requests.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code != 200:
                    return {"success": False, "error": "Ollama server not running"}
            except Exception:
                return {"success": False, "error": "Failed to connect to Ollama server"}

            # Start model pull
            subprocess.Popen(["ollama", "pull", model_name])

            return {
                "success": True,
                "message": f"Started installing {model_name}. This may take several minutes.",
                "model": model_name
            }

        except Exception as e:
            logger.error(f"Error installing Ollama model: {e}")
            return {"success": False, "error": str(e)}

    def check_ollama_status(self) -> Dict[str, Any]:
        """Check Ollama server status"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                return {
                    "running": True,
                    "models_available": len(models),
                    "url": self.ollama_base_url
                }
            else:
                return {"running": False, "error": f"Status code: {response.status_code}"}

        except Exception as e:
            logger.error(f"Error checking Ollama status: {e}")
            return {"running": False, "error": str(e)}


# Example usage
if __name__ == "__main__":
    # Test with Ollama
    ollama_client = LLMClient(provider=LLMProvider.OLLAMA, model_name="codellama")

    # Check status
    status = ollama_client.check_ollama_status()
    print(f"Ollama status: {status}")

    if status.get("running", False):
        # Generate text
        response = ollama_client.generate(
            prompt="Write a Python function to calculate the Fibonacci sequence.",
            max_tokens=500
        )
        print(f"Ollama response: {response}")
