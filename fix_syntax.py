#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to detect and fix syntax errors in Python files using Tree-sitter.
Integrates with AdvancedRAGSystem for query responses and semantic error detection.
"""

import logging
from tree_sitter import Language, Parser
import tree_sitter_languages
from code_parser import CodeEntity
from advanced_rag import AdvancedRAGSystem, RAGResult
from deepseek_embeddings import DeepSeekEmbedder
from qdrant_store import QdrantCodeStore
from code_graph import CodeGraph
import re
import tempfile
import os
from typing import Tuple, List

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SyntaxFixer:
    def __init__(self):
        self.parser = Parser()
        self.parser.set_language(tree_sitter_languages.get_language('python'))

    def fix_syntax(self, file_path: str) -> Tuple[str, List[str]]:
        """Detect and fix syntax errors in a Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return code, [f"Error reading file: {e}"]

        tree = self.parser.parse(bytes(code, 'utf-8'))
        lines = code.split('\n')
        corrected_lines = lines.copy()
        errors = []

        def traverse_node(node, depth=0):
            if node.type == 'ERROR':
                start_line = node.start_point[0]
                end_line = node.end_point[0]
                node_content = code[node.start_byte:node.end_byte]

                # Fix missing except clause in try block
                if 'try' in node_content:
                    parent = node.parent
                    if parent and parent.type == 'try_statement':
                        has_except = any(child.type == 'except_clause' for child in parent.children)
                        has_finally = any(child.type == 'finally_clause' for child in parent.children)
                        if not (has_except or has_finally):
                            indent = '    ' * (depth - 1)
                            corrected_lines.insert(end_line, f"{indent}except Exception as e:")
                            corrected_lines.insert(end_line + 1, f"{indent}    logger.error(f'Error: {{e}}')")
                            errors.append(f"Fixed missing except at line {end_line + 1}")

                # Fix missing colon
                elif node_content.strip().endswith('def') or node_content.strip().endswith('if') or \
                        node_content.strip().endswith('elif') or node_content.strip().endswith('else'):
                    corrected_lines[start_line] = corrected_lines[start_line].rstrip() + ':'
                    errors.append(f"Fixed missing colon at line {start_line + 1}")

            # Check for indentation errors
            if node.type in ('function_definition', 'class_definition', 'if_statement'):
                body = node.child_by_field_name('body')
                if body:
                    first_child = body.children[0] if body.children else None
                    if first_child:
                        expected_indent = '    ' * depth
                        line_idx = first_child.start_point[0]
                        line_content = corrected_lines[line_idx].lstrip()
                        if not corrected_lines[line_idx].startswith(expected_indent):
                            corrected_lines[line_idx] = expected_indent + line_content
                            errors.append(f"Fixed indentation at line {line_idx + 1}")

            for child in node.children:
                traverse_node(child, depth + 1)

        traverse_node(tree.root_node)
        return '\n'.join(corrected_lines), errors

    def detect_undefined_variables(self, code: str) -> List[str]:
        """Detect potential undefined variables using basic regex analysis."""
        undefined_vars = []
        defined_vars = set()
        lines = code.split('\n')

        for i, line in enumerate(lines):
            # Find variable definitions (simple assignments)
            assignments = re.findall(r'(\w+)\s*=\s*[^=]', line)
            defined_vars.update(assignments)

            # Find variable usage (simple identifiers not in strings or comments)
            identifiers = re.findall(r'\b\w+\b', line)
            for ident in identifiers:
                if ident not in defined_vars and not re.match(r'^\d+$', ident) and \
                        ident not in ('True', 'False', 'None') and not line.strip().startswith('#'):
                    undefined_vars.append(f"Potential undefined variable '{ident}' at line {i + 1}")

        return undefined_vars


def process_and_query(file_path: str, query: str, analysis_type: str = "code_explanation") -> Tuple[
    str, List[str], RAGResult]:
    """Process a file, fix errors, and answer a query using AdvancedRAGSystem."""
    # Initialize RAG components
    embedder = DeepSeekEmbedder()
    vector_store = QdrantCodeStore()
    code_graph = CodeGraph()
    rag_system = AdvancedRAGSystem(embedder, vector_store, code_graph)

    # Fix syntax errors
    fixer = SyntaxFixer()
    corrected_code, syntax_errors = fixer.fix_syntax(file_path)

    # Detect undefined variables
    undefined_vars = fixer.detect_undefined_variables(corrected_code)
    errors = syntax_errors + undefined_vars

    # Save corrected code temporarily
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
        temp_file.write(corrected_code)
        temp_file_path = temp_file.name

    # Parse corrected code
    parser = CodeParser()
    entities = parser.parse_file(temp_file_path)

    # Add entities to RAG system
    rag_system.add_code_entities(entities)

    # Query for semantic errors if requested
    semantic_result = None
    if "fix" in query.lower() and "error" in query.lower():
        semantic_result = rag_system.query(
            query="Identify potential semantic errors in the code",
            query_type="general_bug_detection"
        )
        if semantic_result.answer and "Error" not in semantic_result.answer:
            errors.append("Semantic issues detected: " + semantic_result.answer)

    # Process main query
    result = rag_system.query(query, query_type=analysis_type)

    # Clean up temporary file
    os.unlink(temp_file_path)

    return corrected_code, errors, result


if __name__ == "__main__":
    file_path = "otp.py"
    query = "Fix the syntax error in otp.py and explain what the send_otp function does."
    corrected_code, errors, result = process_and_query(file_path, query)

    print("Errors found and fixed:")
    for error in errors:
        print(f"- {error}")
    print("\nCorrected code:")
    print(corrected_code)
    print("\nQuery response:")
    print(result.answer)