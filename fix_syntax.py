#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to detect and fix syntax errors in Python files using Tree-sitter.
Integrates with AdvancedRAGSystem for query responses and semantic error detection.
"""

import logging
import re
import tempfile
import os
import ast
import sys
from typing import Tuple, List, Optional, Dict, Any

# Try to import tree-sitter components with fallback
try:
    from tree_sitter import Language, Parser
    import tree_sitter_languages
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    logging.warning("Tree-sitter not available. Using AST-based parsing instead.")

# Try to import custom RAG components with fallback
try:
    from code_parser import CodeEntity, CodeParser
    from advanced_rag import AdvancedRAGSystem, RAGR<PERSON>ult
    from deepseek_embeddings import DeepSeekEmbedder
    from qdrant_store import QdrantCodeStore
    from code_graph import CodeGraph
    RAG_AVAILABLE = True
except ImportError:
    RAG_AVAILABLE = False
    logging.warning("RAG components not available. Running in basic mode.")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SyntaxFixer:
    def __init__(self):
        self.parser = None
        if TREE_SITTER_AVAILABLE:
            try:
                self.parser = Parser()
                # Try different ways to set the language
                try:
                    python_lang = tree_sitter_languages.get_language('python')
                    self.parser.set_language(python_lang)
                except AttributeError:
                    # Fallback for different tree-sitter versions
                    self.parser.language = tree_sitter_languages.get_language('python')
                logger.info("Tree-sitter parser initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Tree-sitter parser: {e}")
                self.parser = None

    def fix_syntax(self, file_path: str) -> Tuple[str, List[str]]:
        """Detect and fix syntax errors in a Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return "", [f"Error reading file: {e}"]

        # Try Tree-sitter first, fall back to AST-based approach
        if self.parser:
            return self._fix_with_tree_sitter(code)
        else:
            return self._fix_with_ast(code)

    def _fix_with_tree_sitter(self, code: str) -> Tuple[str, List[str]]:
        """Fix syntax using Tree-sitter parser."""
        tree = self.parser.parse(bytes(code, 'utf-8'))
        lines = code.split('\n')
        corrected_lines = lines.copy()
        errors = []

        def traverse_node(node, depth=0):
            if node.type == 'ERROR':
                start_line = node.start_point[0]
                end_line = node.end_point[0]
                node_content = code[node.start_byte:node.end_byte]

                # Fix missing except clause in try block
                if 'try' in node_content:
                    parent = node.parent
                    if parent and parent.type == 'try_statement':
                        has_except = any(child.type == 'except_clause' for child in parent.children)
                        has_finally = any(child.type == 'finally_clause' for child in parent.children)
                        if not (has_except or has_finally):
                            indent = '    ' * max(0, depth - 1)
                            corrected_lines.insert(end_line + 1, f"{indent}except Exception as e:")
                            corrected_lines.insert(end_line + 2, f"{indent}    logger.error(f'Error: {{e}}')")
                            errors.append(f"Fixed missing except at line {end_line + 2}")

                # Fix missing colon
                elif any(keyword in node_content for keyword in ['def ', 'if ', 'elif ', 'else', 'for ', 'while ', 'try:', 'class ']):
                    if start_line < len(corrected_lines) and not corrected_lines[start_line].rstrip().endswith(':'):
                        corrected_lines[start_line] = corrected_lines[start_line].rstrip() + ':'
                        errors.append(f"Fixed missing colon at line {start_line + 1}")

            # Check for indentation errors
            if node.type in ('function_definition', 'class_definition', 'if_statement', 'for_statement', 'while_statement'):
                body = node.child_by_field_name('body')
                if body and body.children:
                    first_child = body.children[0]
                    expected_indent = '    ' * (depth + 1)
                    line_idx = first_child.start_point[0]
                    if line_idx < len(corrected_lines):
                        line_content = corrected_lines[line_idx].lstrip()
                        current_indent = corrected_lines[line_idx][:len(corrected_lines[line_idx]) - len(line_content)]
                        if current_indent != expected_indent and line_content:
                            corrected_lines[line_idx] = expected_indent + line_content
                            errors.append(f"Fixed indentation at line {line_idx + 1}")

            for child in node.children:
                traverse_node(child, depth + 1)

        traverse_node(tree.root_node)
        return '\n'.join(corrected_lines), errors

    def _fix_with_ast(self, code: str) -> Tuple[str, List[str]]:
        """Fix syntax using AST-based approach as fallback."""
        errors = []
        lines = code.split('\n')
        corrected_lines = lines.copy()

        # Multiple passes to fix different types of errors
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            try:
                # Try to parse with AST to detect syntax errors
                ast.parse('\n'.join(corrected_lines))
                break  # No more syntax errors found
            except SyntaxError as e:
                attempt += 1
                error_line = e.lineno - 1 if e.lineno else 0
                error_msg = str(e.msg) if e.msg else "Syntax error"

                if error_line < len(corrected_lines):
                    line = corrected_lines[error_line]
                    original_line = line

                    # Fix common syntax errors
                    if "expected ':'" in error_msg.lower() or "invalid syntax" in error_msg.lower():
                        # Check for missing colon
                        stripped = line.strip()
                        if any(keyword in stripped for keyword in ['def ', 'if ', 'elif ', 'else', 'for ', 'while ', 'try', 'class ', 'except']):
                            if not stripped.endswith(':'):
                                corrected_lines[error_line] = line.rstrip() + ':'
                                errors.append(f"Fixed missing colon at line {error_line + 1}")
                                continue

                    # Fix print statements (Python 2 to 3)
                    if 'print ' in line and not ('print(' in line):
                        # Convert print statement to print function
                        print_match = re.search(r'(\s*)print\s+(.+)', line)
                        if print_match:
                            indent, content = print_match.groups()
                            corrected_lines[error_line] = f"{indent}print({content})"
                            errors.append(f"Fixed print statement at line {error_line + 1}")
                            continue

                    # Fix missing except/finally after try
                    if ("expected 'except' or 'finally'" in error_msg.lower() or
                        ("try" in error_msg.lower() and "except" in error_msg.lower())):
                        # Find the try statement
                        try_line_idx = error_line
                        for i in range(error_line, -1, -1):
                            if 'try:' in corrected_lines[i]:
                                try_line_idx = i
                                break

                        # Find the end of the try block
                        try_indent = len(corrected_lines[try_line_idx]) - len(corrected_lines[try_line_idx].lstrip())
                        indent_str = ' ' * try_indent

                        # Insert except clause after the try block
                        insert_pos = try_line_idx + 1
                        while (insert_pos < len(corrected_lines) and
                               corrected_lines[insert_pos].strip() and
                               len(corrected_lines[insert_pos]) - len(corrected_lines[insert_pos].lstrip()) > try_indent):
                            insert_pos += 1

                        corrected_lines.insert(insert_pos, f"{indent_str}except Exception as e:")
                        corrected_lines.insert(insert_pos + 1, f"{indent_str}    pass")
                        errors.append(f"Added missing except clause at line {insert_pos + 1}")
                        continue

                    # If we couldn't fix this specific error, record it and break
                    if line == original_line:
                        errors.append(f"Syntax error detected: {error_msg} at line {e.lineno}")
                        break
                else:
                    errors.append(f"Syntax error detected: {error_msg} at line {e.lineno}")
                    break

        # Additional pass to fix common issues that AST might miss
        final_lines = []
        for i, line in enumerate(corrected_lines):
            stripped = line.strip()

            # Fix incomplete function/class definitions
            if (stripped.endswith('def') or stripped.endswith('class') or
                stripped.endswith('if') or stripped.endswith('else') or
                stripped.endswith('elif') or stripped.endswith('for') or
                stripped.endswith('while') or stripped.endswith('try')):
                line = line.rstrip() + ':'
                errors.append(f"Fixed incomplete statement at line {i + 1}")

            final_lines.append(line)

        return '\n'.join(final_lines), errors

    def detect_undefined_variables(self, code: str) -> List[str]:
        """Detect potential undefined variables using improved analysis."""
        undefined_vars = []
        defined_vars = set()
        imported_modules = set()
        # Handle builtins properly across different Python versions
        try:
            import builtins
            builtin_names = set(dir(builtins))
        except ImportError:
            builtin_names = set(dir(__builtins__)) if hasattr(__builtins__, '__iter__') else set()

        # Add common Python keywords and built-ins
        python_keywords = {
            'and', 'as', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif', 'else',
            'except', 'finally', 'for', 'from', 'global', 'if', 'import', 'in', 'is', 'lambda',
            'nonlocal', 'not', 'or', 'pass', 'raise', 'return', 'try', 'while', 'with', 'yield',
            'True', 'False', 'None', 'self', 'cls'
        }

        lines = code.split('\n')

        for i, line in enumerate(lines):
            stripped_line = line.strip()

            # Skip comments and empty lines
            if not stripped_line or stripped_line.startswith('#'):
                continue

            # Find imports
            if stripped_line.startswith('import ') or stripped_line.startswith('from '):
                import_match = re.findall(r'import\s+(\w+)', stripped_line)
                imported_modules.update(import_match)
                from_import_match = re.findall(r'from\s+\w+\s+import\s+(.+)', stripped_line)
                if from_import_match:
                    imports = [imp.strip() for imp in from_import_match[0].split(',')]
                    imported_modules.update(imports)

            # Find variable definitions (assignments, function definitions, class definitions)
            assignments = re.findall(r'(\w+)\s*=(?!=)', line)  # Avoid == comparisons
            defined_vars.update(assignments)

            func_defs = re.findall(r'def\s+(\w+)', line)
            defined_vars.update(func_defs)

            class_defs = re.findall(r'class\s+(\w+)', line)
            defined_vars.update(class_defs)

            # Find function parameters
            param_match = re.findall(r'def\s+\w+\s*\(([^)]*)\)', line)
            if param_match:
                params = [p.strip().split('=')[0].strip() for p in param_match[0].split(',') if p.strip()]
                defined_vars.update(params)

        # Second pass: check for undefined variables
        for i, line in enumerate(lines):
            stripped_line = line.strip()
            if not stripped_line or stripped_line.startswith('#') or 'import' in stripped_line:
                continue

            # Find variable usage (excluding strings and comments)
            # Remove string literals to avoid false positives
            line_no_strings = re.sub(r'["\'].*?["\']', '', line)
            identifiers = re.findall(r'\b([a-zA-Z_]\w*)\b', line_no_strings)

            for ident in identifiers:
                if (ident not in defined_vars and
                    ident not in imported_modules and
                    ident not in python_keywords and
                    ident not in builtin_names and
                    not ident.isdigit()):
                    undefined_vars.append(f"Potential undefined variable '{ident}' at line {i + 1}")

        # Remove duplicates while preserving order
        seen = set()
        unique_undefined = []
        for var in undefined_vars:
            if var not in seen:
                seen.add(var)
                unique_undefined.append(var)

        return unique_undefined


def process_and_query(file_path: str, query: str, analysis_type: str = "code_explanation") -> Tuple[str, List[str], Optional[Any]]:
    """Process a file, fix errors, and answer a query using AdvancedRAGSystem."""
    # Fix syntax errors
    fixer = SyntaxFixer()
    corrected_code, syntax_errors = fixer.fix_syntax(file_path)

    # Detect undefined variables
    undefined_vars = fixer.detect_undefined_variables(corrected_code)
    errors = syntax_errors + undefined_vars

    # If RAG components are available, use them
    if RAG_AVAILABLE:
        try:
            # Initialize RAG components
            embedder = DeepSeekEmbedder()
            vector_store = QdrantCodeStore()
            code_graph = CodeGraph()
            rag_system = AdvancedRAGSystem(embedder, vector_store, code_graph)

            # Save corrected code temporarily
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(corrected_code)
                temp_file_path = temp_file.name

            # Parse corrected code
            parser = CodeParser()
            entities = parser.parse_file(temp_file_path)

            # Add entities to RAG system
            rag_system.add_code_entities(entities)

            # Query for semantic errors if requested
            if "fix" in query.lower() and "error" in query.lower():
                semantic_result = rag_system.query(
                    query="Identify potential semantic errors in the code",
                    query_type="general_bug_detection"
                )
                if semantic_result.answer and "Error" not in semantic_result.answer:
                    errors.append("Semantic issues detected: " + semantic_result.answer)

            # Process main query
            result = rag_system.query(query, query_type=analysis_type)

            # Clean up temporary file
            os.unlink(temp_file_path)

            return corrected_code, errors, result

        except Exception as e:
            logger.error(f"Error in RAG processing: {e}")
            errors.append(f"RAG processing error: {e}")

    # Fallback: basic analysis without RAG
    basic_result = _basic_code_analysis(corrected_code, query)
    return corrected_code, errors, basic_result


def _basic_code_analysis(code: str, query: str) -> Dict[str, Any]:
    """Basic code analysis when RAG system is not available."""
    analysis = {
        "answer": "Basic analysis (RAG system not available)",
        "confidence": 0.5,
        "sources": [],
        "analysis_type": "basic"
    }

    lines = code.split('\n')

    # Basic statistics
    total_lines = len(lines)
    non_empty_lines = len([line for line in lines if line.strip()])
    comment_lines = len([line for line in lines if line.strip().startswith('#')])

    # Find functions and classes
    functions = re.findall(r'def\s+(\w+)', code)
    classes = re.findall(r'class\s+(\w+)', code)
    imports = re.findall(r'(?:from\s+\w+\s+)?import\s+(.+)', code)

    analysis["answer"] = f"""
Code Analysis Summary:
- Total lines: {total_lines}
- Non-empty lines: {non_empty_lines}
- Comment lines: {comment_lines}
- Functions found: {len(functions)} ({', '.join(functions[:5])}{'...' if len(functions) > 5 else ''})
- Classes found: {len(classes)} ({', '.join(classes[:3])}{'...' if len(classes) > 3 else ''})
- Import statements: {len(imports)}

Query: {query}
Note: This is a basic analysis. For detailed semantic analysis, please ensure RAG components are available.
"""

    return analysis


def main():
    """Main function to demonstrate the syntax fixer."""
    import argparse

    parser = argparse.ArgumentParser(description="Python Syntax Fixer and Code Analyzer")
    parser.add_argument("file_path", help="Path to the Python file to analyze")
    parser.add_argument("--query", "-q", default="Analyze this code and explain its functionality",
                       help="Query about the code")
    parser.add_argument("--output", "-o", help="Output file for corrected code")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Check if file exists
    if not os.path.exists(args.file_path):
        print(f"Error: File '{args.file_path}' not found.")
        sys.exit(1)

    try:
        corrected_code, errors, result = process_and_query(args.file_path, args.query)

        print("=" * 60)
        print(f"ANALYSIS RESULTS FOR: {args.file_path}")
        print("=" * 60)

        if errors:
            print("\n🔧 ERRORS FOUND AND FIXED:")
            for i, error in enumerate(errors, 1):
                print(f"  {i}. {error}")
        else:
            print("\n✅ NO SYNTAX ERRORS FOUND")

        print(f"\n📊 QUERY: {args.query}")
        print("\n📋 ANALYSIS RESULT:")
        if hasattr(result, 'answer'):
            print(result.answer)
        elif isinstance(result, dict):
            print(result.get('answer', 'No analysis available'))
        else:
            print(str(result))

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(corrected_code)
            print(f"\n💾 CORRECTED CODE SAVED TO: {args.output}")
        elif args.verbose:
            print("\n📝 CORRECTED CODE:")
            print("-" * 40)
            print(corrected_code)
            print("-" * 40)

    except Exception as e:
        logger.error(f"Error processing file: {e}")
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Example usage when run directly
    if len(sys.argv) == 1:
        # Demo mode with default file
        print("🚀 DEMO MODE - Testing with sample file")

        # Create a sample file with syntax errors for testing
        sample_code = '''
def hello_world()
    print "Hello, World!"
    if True
        print("This has syntax errors")

    try:
        x = 1 / 0

class MyClass
    def __init__(self)
        self.value = 42

def calculate(a, b)
    return a + b
'''

        sample_file = "sample_syntax_errors.py"
        with open(sample_file, 'w') as f:
            f.write(sample_code)

        print(f"Created sample file: {sample_file}")

        # Process the sample file
        corrected_code, errors, result = process_and_query(
            sample_file,
            "Fix all syntax errors and explain what this code does"
        )

        print("\n" + "=" * 60)
        print("DEMO RESULTS")
        print("=" * 60)

        print("\n🔧 ERRORS FOUND AND FIXED:")
        for i, error in enumerate(errors, 1):
            print(f"  {i}. {error}")

        print("\n📝 CORRECTED CODE:")
        print("-" * 40)
        print(corrected_code)
        print("-" * 40)

        print("\n📋 ANALYSIS:")
        if hasattr(result, 'answer'):
            print(result.answer)
        elif isinstance(result, dict):
            print(result.get('answer', 'No analysis available'))

        # Clean up
        os.remove(sample_file)
        print(f"\nCleaned up sample file: {sample_file}")

    else:
        main()