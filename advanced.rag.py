#!/usr/bin/env python3
"""
Advanced RAG System using LlamaIndex and LangChain for sophisticated code analysis.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import os
from dataclasses import dataclass

# LlamaIndex imports
from llama_index.core import VectorStoreIndex, Document, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.vector_stores.qdrant import QdrantVectorStore

# LangChain imports
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.schema import Document as LangChainDocument

from code_parser import CodeEntity
from deepseek_embeddings import DeepSeekEmbedder, CodeEmbedding
from qdrant_store import QdrantCodeStore
from code_graph import CodeGraph

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class RAGResult:
    """Result from RAG query"""
    query: str
    answer: str
    retrieved_chunks: List[Dict[str, Any]]
    graph_clues: Dict[str, Any]
    confidence: float
    processing_time: float
    metadata: Dict[str, Any]


class AdvancedRAGSystem:
    """
    Advanced RAG system combining vector search, graph analysis, and LLM reasoning.
    """

    def __init__(self,
                 embedder: DeepSeekEmbedder,
                 vector_store: QdrantCodeStore,
                 code_graph: CodeGraph,
                 llm_client=None):
        """
        Initialize the advanced RAG system

        Args:
            embedder: DeepSeek embedder for code
            vector_store: Qdrant vector store
            code_graph: Code relationship graph
            llm_client: LLM client (Ollama, Gemini, etc.)
        """
        self.embedder = embedder
        self.vector_store = vector_store
        self.code_graph = code_graph
        self.llm_client = llm_client

        # Initialize LlamaIndex components
        self._init_llama_index()

        # Bug detection prompts
        self.bug_prompts = self._create_bug_prompts()

    def _init_llama_index(self):
        """Initialize LlamaIndex components"""
        try:
            # Create Qdrant vector store for LlamaIndex
            self.llama_vector_store = QdrantVectorStore(
                client=self.vector_store.client,
                collection_name=self.vector_store.collection_name
            )

            # Create index
            self.llama_index = VectorStoreIndex.from_vector_store(
                vector_store=self.llama_vector_store
            )

            # Create retriever
            self.retriever = VectorIndexRetriever(
                index=self.llama_index,
                similarity_top_k=10
            )

            # Create query engine
            self.query_engine = RetrieverQueryEngine(
                retriever=self.retriever,
                node_postprocessors=[SimilarityPostprocessor(similarity_cutoff=0.7)]
            )

            logger.info("LlamaIndex components initialized")

        except Exception as e:
            logger.error(f"Error initializing LlamaIndex: {e}")
            self.llama_index = None
            self.query_engine = None

    def _create_bug_prompts(self) -> Dict[str, str]:
        """Create specialized prompts for bug detection"""
        return {
            "general_bug_detection": """
            You are an expert code analyst. Analyze the following code for potential bugs, security vulnerabilities, and code quality issues.

            Code Context:
            {context}

            Graph Relationships:
            {graph_clues}

            Query: {query}

            Please provide:
            1. Identified Issues: List any bugs, vulnerabilities, or problems
            2. Severity: Rate each issue (Critical/High/Medium/Low)
            3. Fix Suggestions: Provide specific code fixes
            4. Explanation: Explain why each issue is problematic

            Focus on:
            - Security vulnerabilities (SQL injection, XSS, etc.)
            - Logic errors and edge cases
            - Performance issues
            - Code quality and maintainability
            - Potential runtime errors
            """,

            "security_analysis": """
            You are a security expert analyzing code for vulnerabilities.

            Code Context:
            {context}

            Graph Relationships:
            {graph_clues}

            Query: {query}

            Analyze for security vulnerabilities:
            1. Input validation issues
            2. Authentication/authorization flaws
            3. Data exposure risks
            4. Injection vulnerabilities
            5. Cryptographic issues
            6. Access control problems

            Provide specific fixes and security best practices.
            """,

            "performance_analysis": """
            You are a performance optimization expert.

            Code Context:
            {context}

            Graph Relationships:
            {graph_clues}

            Query: {query}

            Analyze for performance issues:
            1. Algorithmic complexity problems
            2. Memory usage inefficiencies
            3. Database query optimization
            4. Caching opportunities
            5. Resource management issues

            Provide specific optimization recommendations.
            """,

            "code_explanation": """
            You are a code documentation expert.

            Code Context:
            {context}

            Graph Relationships:
            {graph_clues}

            Query: {query}

            Provide a clear explanation of:
            1. What the code does
            2. How it works (step by step)
            3. Key components and their relationships
            4. Dependencies and interactions
            5. Potential use cases

            Make it understandable for developers at different skill levels.
            """
        }

    def add_code_entities(self, entities: List[CodeEntity]) -> bool:
        """
        Add code entities to the RAG system

        Args:
            entities: List of CodeEntity objects

        Returns:
            True if successful
        """
        try:
            # Generate embeddings
            embeddings = self.embedder.embed_entities(entities)

            # Add to vector store
            success = self.vector_store.add_embeddings(embeddings)

            # Add to graph
            self.code_graph.add_entities(entities)

            logger.info(f"Added {len(entities)} entities to RAG system")
            return success

        except Exception as e:
            logger.error(f"Error adding entities to RAG: {e}")
            return False

    def query(self,
              query: str,
              query_type: str = "general_bug_detection",
              max_chunks: int = 10,
              include_graph_clues: bool = True) -> RAGResult:
        """
        Process a query using the advanced RAG system

        Args:
            query: User query
            query_type: Type of analysis (bug_detection, security, performance, explanation)
            max_chunks: Maximum number of chunks to retrieve
            include_graph_clues: Whether to include graph-based clues

        Returns:
            RAGResult object
        """
        import time
        start_time = time.time()

        try:
            # Step 1: Extract entities from query
            query_entities = self._extract_query_entities(query)

            # Step 2: Retrieve relevant chunks using vector search
            retrieved_chunks = self._retrieve_chunks(query, max_chunks)

            # Step 3: Get graph clues
            graph_clues = {}
            if include_graph_clues and query_entities:
                graph_clues = self.code_graph.get_graph_clues(query_entities)

            # Step 4: Generate response using LLM
            answer = self._generate_response(
                query=query,
                chunks=retrieved_chunks,
                graph_clues=graph_clues,
                query_type=query_type
            )

            processing_time = time.time() - start_time

            # Calculate confidence based on retrieval scores and graph connectivity
            confidence = self._calculate_confidence(retrieved_chunks, graph_clues)

            return RAGResult(
                query=query,
                answer=answer,
                retrieved_chunks=retrieved_chunks,
                graph_clues=graph_clues,
                confidence=confidence,
                processing_time=processing_time,
                metadata={
                    "query_entities": query_entities,
                    "query_type": query_type,
                    "chunks_retrieved": len(retrieved_chunks)
                }
            )

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return RAGResult(
                query=query,
                answer=f"Error processing query: {str(e)}",
                retrieved_chunks=[],
                graph_clues={},
                confidence=0.0,
                processing_time=time.time() - start_time,
                metadata={"error": str(e)}
            )

    def _extract_query_entities(self, query: str) -> List[str]:
        """Extract potential entity names from query"""
        # Simple entity extraction - could be enhanced with NLP
        import re

        # Look for camelCase, PascalCase, and snake_case identifiers
        patterns = [
            r'\b[A-Z][a-zA-Z0-9]*\b',  # PascalCase
            r'\b[a-z][a-zA-Z0-9]*[A-Z][a-zA-Z0-9]*\b',  # camelCase
            r'\b[a-z_][a-z0-9_]*\b'  # snake_case
        ]

        entities = []
        for pattern in patterns:
            matches = re.findall(pattern, query)
            entities.extend(matches)

        # Filter out common words
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        entities = [e for e in entities if e.lower() not in common_words and len(e) > 2]

        return list(set(entities))  # Remove duplicates

    def _retrieve_chunks(self, query: str, max_chunks: int) -> List[Dict[str, Any]]:
        """Retrieve relevant code chunks using vector search"""
        try:
            # Use Qdrant for vector search
            results = self.vector_store.search_by_text(
                query_text=query,
                embedder=self.embedder,
                limit=max_chunks
            )

            return results

        except Exception as e:
            logger.error(f"Error retrieving chunks: {e}")
            return []

    def _generate_response(self,
                           query: str,
                           chunks: List[Dict[str, Any]],
                           graph_clues: Dict[str, Any],
                           query_type: str) -> str:
        """Generate response using LLM with context and graph clues"""
        try:
            # Prepare context from retrieved chunks
            context = self._format_context(chunks)

            # Format graph clues
            graph_context = self._format_graph_clues(graph_clues)

            # Get appropriate prompt
            prompt_template = self.bug_prompts.get(query_type, self.bug_prompts["general_bug_detection"])

            # Format prompt
            formatted_prompt = prompt_template.format(
                query=query,
                context=context,
                graph_clues=graph_context
            )

            # Generate response using LLM
            if self.llm_client:
                response = self.llm_client.generate(formatted_prompt)
                return response
            else:
                # Fallback response
                return self._generate_fallback_response(query, chunks, graph_clues)

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error generating response: {str(e)}"

    def _format_context(self, chunks: List[Dict[str, Any]]) -> str:
        """Format retrieved chunks into context string"""
        if not chunks:
            return "No relevant code found."

        context_parts = []
        for i, chunk in enumerate(chunks[:5]):  # Limit to top 5
            context_parts.append(f"""
File: {chunk.get('file_path', 'Unknown')}
Entity: {chunk.get('entity_name', 'Unknown')} ({chunk.get('entity_type', 'Unknown')})
Relevance Score: {chunk.get('score', 0.0):.3f}

Code:
{chunk.get('metadata', {}).get('content', 'No content available')}
---
""")

        return "\n".join(context_parts)

    def _format_graph_clues(self, graph_clues: Dict[str, Any]) -> str:
        """Format graph clues into readable string"""
        if not graph_clues:
            return "No graph relationships found."

        clues_text = []

        # Related entities
        if graph_clues.get('related_entities'):
            clues_text.append("Related Entities:")
            for entity in graph_clues['related_entities'][:10]:
                clues_text.append(f"- {entity['name']} ({entity['type']}) in {entity['file_path']}")

        # Relationship paths
        if graph_clues.get('relationship_paths'):
            clues_text.append("\nRelationship Paths:")
            for path in graph_clues['relationship_paths'][:5]:
                clues_text.append(f"- {path['source']} → {path['target']} (path length: {path['length']})")

        return "\n".join(clues_text) if clues_text else "No specific relationships found."

    def _calculate_confidence(self, chunks: List[Dict[str, Any]], graph_clues: Dict[str, Any]) -> float:
        """Calculate confidence score for the response"""
        if not chunks:
            return 0.0

        # Base confidence from retrieval scores
        avg_score = sum(chunk.get('score', 0.0) for chunk in chunks) / len(chunks)

        # Boost confidence if we have graph clues
        graph_boost = 0.1 if graph_clues.get('related_entities') else 0.0

        # Boost confidence if we have multiple relevant chunks
        chunk_boost = min(0.2, len(chunks) * 0.02)

        confidence = min(1.0, avg_score + graph_boost + chunk_boost)
        return confidence

    def _generate_fallback_response(self, query: str, chunks: List[Dict[str, Any]], graph_clues: Dict[str, Any]) -> str:
        """Generate fallback response when LLM is not available"""
        if not chunks:
            return "No relevant code found for your query."

        response_parts = [
            f"Found {len(chunks)} relevant code segments for your query.",
            "\nMost relevant findings:"
        ]

        for i, chunk in enumerate(chunks[:3]):
            response_parts.append(f"""
{i + 1}. {chunk.get('entity_name', 'Unknown')} in {chunk.get('file_path', 'Unknown')}
   Type: {chunk.get('entity_type', 'Unknown')}
   Relevance: {chunk.get('score', 0.0):.3f}
""")

        if graph_clues.get('related_entities'):
            response_parts.append(f"\nRelated entities found: {len(graph_clues['related_entities'])}")

        return "\n".join(response_parts)


# Example usage
if __name__ == "__main__":
    # Initialize components
    embedder = DeepSeekEmbedder()
    vector_store = QdrantCodeStore()
    code_graph = CodeGraph()

    # Create RAG system
    rag_system = AdvancedRAGSystem(
        embedder=embedder,
        vector_store=vector_store,
        code_graph=code_graph
    )

    # Example query
    result = rag_system.query("Find security vulnerabilities in the authentication system")
    print(f"Answer: {result.answer}")
    print(f"Confidence: {result.confidence}")
    print(f"Processing time: {result.processing_time:.2f}s")
